/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.dataprovider.payment;

import com.zatech.gaia.resource.components.enums.paymentgateway.PayStatusEnum;
import com.zatech.genesis.customer.portal.biz.common.uimodel.dataprovider.payment.request.PaymentResultParam;
import com.zatech.genesis.customer.portal.biz.common.uimodel.dataprovider.payment.result.PaymentResult;
import com.zatech.genesis.customer.portal.common.event.PayResultEvent;
import com.zatech.genesis.customer.portal.common.payment.PaymentManager;
import com.zatech.genesis.customer.portal.common.payment.response.QueryPaymentInfoOutput;
import com.zatech.genesis.customer.portal.integration.outer.payment.IPaymentApi;
import com.zatech.genesis.payment.gateway.api.message.PaymentGatewayMsgBody;
import com.zatech.genesis.payment.gateway.api.response.PayOrderResp;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.DataProvideContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.DataProviderKind;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.IDataProvider;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.UIModelDataProvider;
import com.zatech.genesis.portal.lowcode.framework.common.enums.FreeMartProductCategoryEnum;

import java.util.Objects;
import java.util.Optional;

import lombok.AllArgsConstructor;

import org.springframework.context.ApplicationEventPublisher;

/**
 * @Author: weizhen.kong
 */
@UIModelDataProvider(
    name = "paymentResult",
    productCategories = {FreeMartProductCategoryEnum.ALL},
    kind = DataProviderKind.data,
    desc = "查询是否支付成功")
@AllArgsConstructor
public class PaymentResultDataProvider implements IDataProvider<PaymentResultParam, PaymentResult> {

    private final IPaymentApi paymentApi;

    private final PaymentManager paymentManager;

    private ApplicationEventPublisher eventPublisher;

    @Override
    public PaymentResult provide(PaymentResultParam unused, DataProvideContext dataProvideContext) {
        Optional<QueryPaymentInfoOutput> queryPaymentInfoOutput = paymentManager.queryPaymentInfoOtp(dataProvideContext.getOrder().getOrderNo());

        return queryPaymentInfoOutput.map(x -> {
                if (Objects.equals(x.getStatus(), PayStatusEnum.PAYING)) {
                    PayOrderResp payOrderResp = paymentApi.queryPayOrder(x.getThirdPartyOrderNo());
                    PaymentGatewayMsgBody paymentGatewayMsgBody = new PaymentGatewayMsgBody();
                    paymentGatewayMsgBody.setPayOrderNo(payOrderResp.getPayOrderNo());
                    paymentGatewayMsgBody.setPayStatus(payOrderResp.getPayStatus());
                    paymentGatewayMsgBody.setTransactionNo(payOrderResp.getPayBusinessNo());
                    paymentGatewayMsgBody.setThirdPartyNo(payOrderResp.getThirdPartyNo());
                    paymentGatewayMsgBody.setThirdPartyNo2(payOrderResp.getThirdPartyNo2());
                    eventPublisher.publishEvent(new PayResultEvent(paymentGatewayMsgBody));
                }
                return paymentManager.queryPaymentInfoOtp(x.getOrderNo()).map(PaymentResult::from).orElse(new PaymentResult());
            })
            .orElse(new PaymentResult());
    }

}