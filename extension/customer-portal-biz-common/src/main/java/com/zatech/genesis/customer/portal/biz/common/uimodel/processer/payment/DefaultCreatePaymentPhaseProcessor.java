/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.processer.payment;

import com.za.cqrs.util.Functions;
import com.zatech.gaia.resource.components.enums.paymentgateway.PayStatusEnum;
import com.zatech.genesis.customer.portal.biz.common.uimodel.processer.payment.result.CreatePaymentResult;
import com.zatech.genesis.customer.portal.common.exception.PaymentErrorCodes;
import com.zatech.genesis.customer.portal.common.payment.PaymentManager;
import com.zatech.genesis.customer.portal.common.payment.request.CancelPaymentInput;
import com.zatech.genesis.customer.portal.common.payment.request.CreatePaymentInput;
import com.zatech.genesis.customer.portal.common.payment.response.CreatePaymentOutput;
import com.zatech.genesis.customer.portal.common.payment.response.QueryPaymentInfoOutput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.payment.request.PaymentOrderRequest;
import com.zatech.genesis.customer.portal.integration.outer.openapi.payment.response.PayOrderResp;
import com.zatech.genesis.customer.portal.integration.outer.payment.PaymentAdapter;
import com.zatech.genesis.payment.gateway.api.request.PayCancelReq;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.IPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.PaymentPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.context.OrderContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.phase.IUIModelPhasePostProcessor;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.util.JsonParser;
import com.zatech.genesis.portal.toolbox.exception.CommonException;
import com.zatech.genesis.portal.toolbox.share.json.model.JsonMap;

import java.util.Date;
import java.util.Objects;
import java.util.Optional;

import javax.annotation.Nullable;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DefaultCreatePaymentPhaseProcessor implements IUIModelPhasePostProcessor<PaymentOrderRequest> {

    @Autowired
    private PaymentAdapter paymentAdapter;

    @Autowired
    private PaymentManager paymentManager;

    @Override
    public IPhase[] forPhases() {
        return new IPhase[] {PaymentPhase.payment};
    }

    @Override
    public Result process(IPhase iPhase, PaymentOrderRequest paymentOrderRequest, BusinessHandleContext businessHandleContext) {
        Optional<QueryPaymentInfoOutput> queryPaymentInfoOutput = paymentManager.queryPaymentInfoOtp(businessHandleContext.getOrder().getOrderNo());
        boolean existPayed = queryPaymentInfoOutput
            .filter(p -> Objects.equals(p.getStatus(), PayStatusEnum.SUCCESS))
            .isPresent();
        boolean existPaying = queryPaymentInfoOutput
            .filter(p -> Objects.equals(p.getStatus(), PayStatusEnum.PAYING))
            .isPresent();
        Functions.doIf(existPayed, () -> {throw CommonException.byError(PaymentErrorCodes.ORDER_PAYMENT_JUDGE_ORDER_STATUS_PAYED);});
        Functions.doIf(existPaying, () -> {
            if (paymentOrderRequest.isRecreatePayment()) {
                queryPaymentInfoOutput.ifPresent(existPayment -> {
                    CancelPaymentInput cancelPaymentInput = new CancelPaymentInput();
                    cancelPaymentInput.setPayOrderNo(existPayment.getThirdPartyOrderNo());
                    cancelPaymentInput.setOrderNo(existPayment.getOrderNo());
                    paymentManager.cancelPay(cancelPaymentInput, businessHandleContext);

                    PayCancelReq cancelPayReq = new PayCancelReq();
                    cancelPayReq.setPayOrderNo(existPayment.getThirdPartyOrderNo());
                    log.info("DefaultCancelPaymentPhaseProcessor.process payCancelReq:{}", JsonParser.toJsonString(cancelPayReq));
                    PayOrderResp cancelPaymentResult = paymentAdapter.cancel(cancelPayReq);
                    log.info("DefaultCancelPaymentPhaseProcessor.process cancelPaymentResult:{}", JsonParser.toJsonString(cancelPaymentResult));
                });
            } else {
                throw CommonException.byError(PaymentErrorCodes.ORDER_PAYMENT_JUDGE_ORDER_STATUS_PAYED);
            }
        });

        CreatePaymentInput createPaymentInput;
        try {
            log.info("DefaultCreatePaymentPhaseProcessor.process paymentOderRequest:{}", JsonParser.toJsonString(paymentOrderRequest));
            PayOrderResp payOrderResp = paymentAdapter.pay(paymentOrderRequest);
            log.info("DefaultCreatePaymentPhaseProcessor.process payOrderResp:{}", JsonParser.toJsonString(payOrderResp));
            createPaymentInput = toCreatePaymentInput(payOrderResp, paymentOrderRequest, businessHandleContext);
        } catch (Exception e) {
            log.warn("DefaultCreatePaymentPhaseProcessor.process exception ", e);
            createPaymentInput = toCreatePaymentInput(null, paymentOrderRequest, businessHandleContext);
            createPaymentInput.setPayDate(new Date());
            createPaymentInput.setStatus(PayStatusEnum.FAILED);
        }
        CreatePaymentOutput createPaymentOutput = paymentManager.create(createPaymentInput);
        createPaymentOutput.setFormParams(createPaymentInput.getParams());
        createPaymentOutput.setRedirectUrl(createPaymentInput.getRedirectUrl());
        return CreatePaymentResult.from(createPaymentOutput);
    }

    private CreatePaymentInput toCreatePaymentInput(@Nullable PayOrderResp pay, PaymentOrderRequest paymentOrderRequest, BusinessHandleContext businessHandleContext) {
        OrderContext order = businessHandleContext.getOrder();
        CreatePaymentInput createPaymentInput = new CreatePaymentInput();
        createPaymentInput.setOrderId(order.getOrderId());
        createPaymentInput.setOrderNo(order.getOrderNo());
        createPaymentInput.setAdditional(new JsonMap(paymentOrderRequest.getExtensionParams()));
        createPaymentInput.setAmount(paymentOrderRequest.getAmount());
        createPaymentInput.setPaymentMethod(paymentOrderRequest.getPayMethod());
        createPaymentInput.setChannel(paymentOrderRequest.getPayChannel());
        createPaymentInput.setCurrency(paymentOrderRequest.getCurrency() == null ? null : paymentOrderRequest.getCurrency().name());
        createPaymentInput.setPayDate(Optional.ofNullable(pay).map(PayOrderResp::getPayDate).orElse(new Date()));
        createPaymentInput.setStatus(pay == null ? PayStatusEnum.FAILED : pay.getPayStatus());
        Optional.ofNullable(pay).map(PayOrderResp::getPayOrderNo).ifPresent(createPaymentInput::setThirdPartyOrderNo);
        Optional.ofNullable(pay).map(PayOrderResp::getRedirectUrl).ifPresent(createPaymentInput::setRedirectUrl);
        Optional.ofNullable(pay).map(PayOrderResp::getFormParams).ifPresent(createPaymentInput::addParams);
        createPaymentInput.setFrontendCancelUrl(paymentOrderRequest.getCancelUrl());
        createPaymentInput.setFrontendErrorUrl(paymentOrderRequest.getFailUrl());
        createPaymentInput.setFrontendReturnUrl(paymentOrderRequest.getSuccessUrl());
        return createPaymentInput;
    }

}
