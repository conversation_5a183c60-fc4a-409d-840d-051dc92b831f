package com.zatech.genesis.customer.portal.biz.common.uimodel;

import com.zatech.genesis.portal.lowcode.framework.client.sdk.IUIModel;

import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public abstract class BaseModel implements Serializable, IUIModel {

    @Schema(description = "扩展字段")
    private Map<String, String> extensions;

    public BaseModel addExtension(String key, String value) {
        if (this.extensions == null) {
            this.extensions = new HashMap<>();
        }
        this.extensions.put(key, value);
        return this;
    }

}