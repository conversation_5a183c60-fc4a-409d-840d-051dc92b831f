package com.zatech.genesis.customer.portal.biz.common.uimodel.outer.market.fallback;

import com.zatech.genesis.customer.portal.biz.common.uimodel.outer.market.IOuterMarketService;
import com.zatech.genesis.market.api.structure.request.PackageApplicationElementsEnumRequest;
import com.zatech.genesis.market.api.structure.request.QueryGoodsRelatingRequest;
import com.zatech.genesis.market.api.structure.response.GoodsRelatingResponse;
import com.zatech.genesis.market.api.structure.response.PackageApplicationElementsEnumResponse;
import com.zatech.octopus.core.util.JacksonUtil;

import lombok.extern.slf4j.Slf4j;

import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/11/16
 */
@Slf4j
@Component
public class IOuterCommonMarketServiceFallbackFactory implements FallbackFactory<IOuterMarketService> {
    private static final String ERROR = "\n <<[Method]: {}\n[request info]: {}\n[cause]: {}";

    @Override
    public IOuterMarketService create(Throwable cause) {
        return new IOuterMarketService() {
            @Override
            public GoodsRelatingResponse queryGoodsRelating(QueryGoodsRelatingRequest request) {
                log.warn(ERROR, "RPC>Fallback>>IOuterCdcService.queryGoodsRelating", JacksonUtil.toJSONString(request), cause.getMessage());
                return null;
            }

            @Override
            public PackageApplicationElementsEnumResponse queryPackageApplicationElementEnumItem(PackageApplicationElementsEnumRequest request) {
                return null;
            }
        };
    }
}
