/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.dataprovider.googlemap.extractor;

import com.zatech.genesis.customer.portal.biz.common.uimodel.dataprovider.googlemap.param.GoogleMapParam;
import com.zatech.genesis.customer.portal.biz.common.uimodel.dataprovider.googlemap.result.GoogleMapResult;
import com.zatech.genesis.customer.portal.biz.common.uimodel.errorcode.CommonErrorCodes;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.DataProvideContext;
import com.zatech.genesis.portal.toolbox.exception.CommonException;

import java.util.List;

import org.springframework.stereotype.Component;

@Component
public class GoogleMapExtractorManager {

    private final List<IGoogleMapExtractor> extractorList;

    public GoogleMapExtractorManager(List<IGoogleMapExtractor> extractorList) {
        this.extractorList = extractorList;
    }

    public GoogleMapResult extract(GoogleMapParam param, DataProvideContext context) {
        var extractTypeParam = context.getParams().get("type");
        if (extractTypeParam == null) {
            throw CommonException.byError(CommonErrorCodes.google_map_extract_type_is_null);
        }
        var extractType = GoogleMapExtractTypeEnum.valueOf(extractTypeParam.toString());
        var extractorOpt = extractorList.stream().filter(ex -> ex.match(extractType)).findFirst();
        return extractorOpt.map(ex -> ex.extract(context, extractType))
            .orElseThrow(() -> CommonException.byErrorAndParams(CommonErrorCodes.google_map_extractor_not_found, extractType));
    }
}
