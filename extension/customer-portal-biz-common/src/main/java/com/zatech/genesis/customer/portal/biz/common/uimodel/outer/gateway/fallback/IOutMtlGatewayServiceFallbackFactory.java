/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.outer.gateway.fallback;

import com.zatech.genesis.customer.portal.biz.common.uimodel.outer.gateway.IOutMtlGatewayService;
import com.zatech.genesis.customer.portal.biz.common.uimodel.outer.gateway.errorcode.IntGatewayServiceErrorCode;
import com.zatech.genesis.customer.portal.biz.common.uimodel.outer.gateway.request.GoogleCompletionRequest;
import com.zatech.genesis.customer.portal.biz.common.uimodel.outer.gateway.response.GroupItemResponse;
import com.zatech.genesis.gateway.api.googleMap.dto.GoogleCompletionResponse;
import com.zatech.genesis.gateway.api.googleMap.dto.GooglePlaceDetailsResponse;
import com.zatech.genesis.gateway.api.googleMap.dto.MapCityResponse;
import com.zatech.genesis.gateway.api.googleMap.dto.MapCountryResponse;
import com.zatech.genesis.portal.toolbox.exception.errorcode.IErrorCode;
import com.zatech.genesis.portal.toolbox.exception.feign.AbstractFallbackFactory;
import com.zatech.genesis.portal.toolbox.exception.mapping.ISourceErrorMapping;
import com.zatech.genesis.portal.toolbox.exception.mapping.ISourceErrorMappingDefault;
import com.zatech.genesis.portal.toolbox.share.json.StaticJsonParser;

import java.util.List;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

@Slf4j
@Component
public class IOutMtlGatewayServiceFallbackFactory extends AbstractFallbackFactory<IOutMtlGatewayService> implements ISourceErrorMapping,
        ISourceErrorMappingDefault {

    @Override
    public IOutMtlGatewayService create(Throwable cause) {
        return new IOutMtlGatewayService() {

            @Override
            public List<GroupItemResponse> group(String groupName) {
                throw outerServiceException(cause, String.format("groupName: %s", groupName));
            }

            @Override
            public List<GoogleCompletionResponse> queryGoogleAddress(String searchText, String country) {
                throw outerServiceException(cause, String.format("searchText: %s, country: %s ", searchText, country));
            }

            @Override
            public List<GoogleCompletionResponse> queryGoogleAddressSearch(GoogleCompletionRequest request) {
                throw outerServiceException(cause, StaticJsonParser.toJsonString(request));
            }

            @Override
            public GooglePlaceDetailsResponse queryAddressDetails(String placeId) {
                throw outerServiceException(cause, String.format("placeId: %s", placeId));
            }

            @Override
            public MapCountryResponse queryCountryList() {
                throw outerServiceException(cause, "query country list");
            }

            @Override
            public MapCityResponse queryCityList(String numericCode, String postCode) {
                throw outerServiceException(cause, String.format("numericCode: %s, postCode: %s ", numericCode, postCode));
            }

        };
    }

    @Override
    public Class<? extends IErrorCode>[] mappingErrorCodes() {
        return new Class[] {IntGatewayServiceErrorCode.class};
    }

    @Override
    public IErrorCode defaultErrorCode() {
        return IntGatewayServiceErrorCode.SERVICE_FAILED;
    }

}
