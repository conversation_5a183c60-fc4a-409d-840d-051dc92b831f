/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.dataprovider.googlemap.result;

import com.zatech.genesis.gateway.api.googleMap.dto.GooglePlaceDetailsResponse;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Setter
@Getter
@Accessors(chain = true)
public class GoogleMapPlaceIdDetailResult extends GoogleMapResult {

    private String formattedAddress;

    private String longitude;

    private String latitude;

    private String streetNo;

    private String streetName;

    private String city;

    private String province;

    private String country;

    private String postCode;

    public static GoogleMapPlaceIdDetailResult from(GooglePlaceDetailsResponse response) {
        return new GoogleMapPlaceIdDetailResult()
            .setFormattedAddress(response.getFormattedAddress())
            .setLongitude(response.getLongitude())
            .setLatitude(response.getLatitude())
            .setStreetNo(response.getStreetNo())
            .setStreetName(response.getStreetName())
            .setCity(response.getCity())
            .setProvince(response.getProvince())
            .setCountry(response.getCountry())
            .setPostCode(response.getPostCode());
    }
}
