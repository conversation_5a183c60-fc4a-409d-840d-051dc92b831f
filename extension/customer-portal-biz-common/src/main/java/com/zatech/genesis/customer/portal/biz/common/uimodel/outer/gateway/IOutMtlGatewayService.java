/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.outer.gateway;

import com.zatech.genesis.customer.portal.biz.common.uimodel.outer.gateway.fallback.IOutMtlGatewayServiceFallbackFactory;
import com.zatech.genesis.customer.portal.biz.common.uimodel.outer.gateway.request.GoogleCompletionRequest;
import com.zatech.genesis.customer.portal.biz.common.uimodel.outer.gateway.response.GroupItemResponse;
import com.zatech.genesis.gateway.api.googleMap.dto.GoogleCompletionResponse;
import com.zatech.genesis.gateway.api.googleMap.dto.GooglePlaceDetailsResponse;
import com.zatech.genesis.gateway.api.googleMap.dto.MapCityResponse;
import com.zatech.genesis.gateway.api.googleMap.dto.MapCountryResponse;

import java.util.List;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "zatech-int-gateway", url = "${genesis-feign-int-gateway}", fallbackFactory = IOutMtlGatewayServiceFallbackFactory.class)
public interface IOutMtlGatewayService {

    @GetMapping("/google-map/places/{address-search}/")
    List<GoogleCompletionResponse> queryGoogleAddress(@PathVariable("address-search") String searchText, @RequestParam(name = "country", required = false) String country);

    @PostMapping("/google-map/places/address-search")
    List<GoogleCompletionResponse> queryGoogleAddressSearch(@RequestBody GoogleCompletionRequest request);

    @GetMapping("/api/v2/map/group/{group-name}/")
    List<GroupItemResponse> group(@PathVariable("group-name") String groupName);

    @GetMapping("/google-map/places/details/{place-id}/")
    GooglePlaceDetailsResponse queryAddressDetails(@PathVariable("place-id") String placeId);

    // country
    @GetMapping("/map/places/country/list")
    MapCountryResponse queryCountryList();

    // city
    @GetMapping("/map/places/country/{numeric-code}/city/{post-code}/list")
    MapCityResponse queryCityList(@PathVariable("numeric-code") String numericCode, @PathVariable("post-code") String postCode);




}
