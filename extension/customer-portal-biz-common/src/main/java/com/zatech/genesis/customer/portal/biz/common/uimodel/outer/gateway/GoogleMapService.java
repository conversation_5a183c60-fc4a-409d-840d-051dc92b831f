/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.outer.gateway;

import com.zatech.genesis.customer.portal.biz.common.uimodel.outer.gateway.response.GroupItemResponse;
import com.zatech.genesis.gateway.api.googleMap.dto.GoogleCompletionResponse;
import com.zatech.genesis.gateway.api.googleMap.dto.GooglePlaceDetailsResponse;
import com.zatech.genesis.gateway.api.googleMap.dto.MapCityResponse;
import com.zatech.genesis.gateway.api.googleMap.dto.MapCountryResponse;

import java.util.List;

public interface GoogleMapService {

    List<GoogleCompletionResponse> queryGoogleAddress(String searchText, String country);

    GooglePlaceDetailsResponse queryAddressDetails(String placeId);

    List<GroupItemResponse> group(String groupName);

    MapCountryResponse queryCountryList();

    MapCityResponse queryCityList(String numericCode, String postCode);

}
