/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.lifecycle.uimodel;

import com.zatech.genesis.customer.portal.biz.common.uimodel.CommonModel;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.lifecycle.IUIModelLifecycleProcessor;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.lifecycle.context.OrderLifecycleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.input.CreateUIModelInput;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.input.UpdateUIModelInput;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.output.UIModelOrderOutput;

import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ValidatePosOrderUimodelProcessor implements IUIModelLifecycleProcessor<CommonModel> {

    @Autowired
    private PolicyPermissionCheckService policyPermissionCheckService;

    @Override
    public int order() {
        return 0;
    }

    @Override
    public void beforeCreateUIModel(OrderLifecycleContext context, CreateUIModelInput<CommonModel> input) {
        doPolicyCheck(input.getData());
    }

    private void doPolicyCheck(CommonModel input) {
        Optional.ofNullable(input).map(CommonModel::getPolicyNo).ifPresent(policyNo -> policyPermissionCheckService.check(policyNo));
    }

    @Override
    public void beforeUpdateUIModel(OrderLifecycleContext context, UIModelOrderOutput<CommonModel> currentOrder, UpdateUIModelInput<CommonModel> input) {
        doPolicyCheck(input.getData());
    }

    @Override
    public void beforeResetUIModel(OrderLifecycleContext context, UIModelOrderOutput<CommonModel> currentOrder, UpdateUIModelInput<CommonModel> resetInput) {
        doPolicyCheck(resetInput.getData());
    }

}
