/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel;

/**
 * @Author: weizhen.kong
 */
public class EnumConstants {

    private EnumConstants() {}

    public static final String GROUP = "group";

    public static final String CODE = "code";

    public static final String CASCADE = "cascade";

    public static final String POLICY = "policyItems";

    public static final String HOLDER = "holderItems";

    public static final String INSURED = "insuredItems";

    public static final String PAYER = "payerItems";

    public static final String OBJECT = "objectItems";

    public static final String VEHICLE_GROUP = "vehicleGroup";

    public static final String NUMBER_OF_SEAT = "numberOfSeat";

    public static final String VEHICLE_COLOR = "vehicleColor";

    public static final String TRUCK_TYPE = "truckType";

}