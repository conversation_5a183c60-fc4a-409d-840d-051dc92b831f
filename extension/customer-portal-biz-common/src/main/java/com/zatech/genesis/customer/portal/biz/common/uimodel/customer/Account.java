/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.customer;

import com.zatech.gaia.resource.graphene.customer.AccountSubTypeEnum;
import com.zatech.gaia.resource.graphene.customer.AccountTypeEnum;
import com.zatech.genesis.customer.portal.biz.common.uimodel.DynamicMetaField;

import java.time.LocalDate;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class Account extends DynamicMetaField {

    private String paymentMethod;

    private String bankCode;

    private String bankBranchCode;

    private String bankBranchName;

    private String bankCity;

    private AccountTypeEnum accountType;

    private AccountSubTypeEnum accountSubType;

    private String cardHolderName;

    private String cardNumber;

    private LocalDate expiryDate;

    private String mobileNo;

    /**
     * 安全码，信用卡后面数字三位
     */
    private String safeNo;

    private String bankName;

}
