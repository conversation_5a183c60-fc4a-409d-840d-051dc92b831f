package com.zatech.genesis.customer.portal.biz.common.uimodel;

import com.zatech.genesis.portal.lowcode.framework.infra.IdGenerator;

import java.time.LocalDateTime;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 订单号生成器
 * 基于时间戳生成9位不重复的订单号
 */
public class OrderNumberGenerator {
    
    /**
     * 生成9位订单号
     * 格式：2位年份后缀 + 1位月份编码 + 1位日期编码 + 2位小时分钟编码 + 3位随机数
     * 
     * @return 9位订单号
     */
    public static String generateOrderNumber() {
        LocalDateTime now = LocalDateTime.now();
        
        // 获取年份后两位 (00-99)
        String year = String.format("%02d", now.getYear() % 100);
        
        // 将月份转为一位字符 (1-9,A,B,C)
        char month = monthToChar(now.getMonthValue());
        
        // 将日期转为一位字符 (1-9,A-V)
        char day = dayToChar(now.getDayOfMonth());
        
        // 将小时和分钟压缩为两位数字
        String hourMinute = encodeHourMinute(now.getHour(), now.getMinute());
        
        // 生成3位随机数
        String random = String.format("%03d", ThreadLocalRandom.current().nextInt(1000));
        
        // 组合成9位订单号
        return year + month + day + hourMinute + random;
    }
    
    /**
     * 将月份转为一位字符
     * 1-9 -> '1'-'9', 10 -> 'A', 11 -> 'B', 12 -> 'C'
     */
    private static char monthToChar(int month) {
        if (month <= 9) {
            return (char) ('0' + month);
        } else {
            return (char) ('A' + (month - 10));
        }
    }

    /**
     * 将日期转为一位字符
     * 1-9 -> '1'-'9', 10-35 -> 'A'-'Z'
     */
    private static char dayToChar(int day) {
        if (day <= 9) {
            return (char) ('0' + day);
        } else {
            return (char) ('A' + (day - 10));
        }
    }
    
    /**
     * 将小时和分钟编码为两位数字
     * 将24小时和60分钟压缩到两位数字中
     */
    private static String encodeHourMinute(int hour, int minute) {
        // 将小时和分钟转换为总分钟数 (0-1439)
        int totalMinutes = hour * 60 + minute;
        
        // 将总分钟数映射到00-99范围
        int encoded = (totalMinutes * 100) / 1440;
        
        return String.format("%02d", encoded);
    }
    
    /**
     * 生成带前缀的订单号
     * 
     * @param prefix 前缀
     * @return 带前缀的订单号
     */
    public static String generateOrderNumber(String prefix) {
        return prefix + IdGenerator.generateId();
    }

}