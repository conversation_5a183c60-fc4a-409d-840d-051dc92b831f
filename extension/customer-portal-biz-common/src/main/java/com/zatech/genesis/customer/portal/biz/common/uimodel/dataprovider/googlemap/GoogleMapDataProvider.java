/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.dataprovider.googlemap;

import com.zatech.genesis.customer.portal.biz.common.uimodel.dataprovider.googlemap.extractor.GoogleMapExtractorManager;
import com.zatech.genesis.customer.portal.biz.common.uimodel.dataprovider.googlemap.param.GoogleMapParam;
import com.zatech.genesis.customer.portal.biz.common.uimodel.dataprovider.googlemap.result.GoogleMapResult;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.DataProvideContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.DataProviderKind;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.IAuthDataProvider;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.UIModelDataProvider;
import com.zatech.genesis.portal.lowcode.framework.common.enums.FreeMartProductCategoryEnum;

import jakarta.annotation.Resource;

@UIModelDataProvider(
    name = "googleMap",
    productCategories = {FreeMartProductCategoryEnum.ALL},
    kind = DataProviderKind.data,
    desc = "查询google地图信息")
public class GoogleMapDataProvider implements IAuthDataProvider<GoogleMapParam, GoogleMapResult> {

    @Resource
    private GoogleMapExtractorManager googleMapExtractorManager;

    @Override
    public GoogleMapResult provide(GoogleMapParam googleMapParam, DataProvideContext dataProvideContext) {
        return googleMapExtractorManager.extract(googleMapParam, dataProvideContext);
    }

}
