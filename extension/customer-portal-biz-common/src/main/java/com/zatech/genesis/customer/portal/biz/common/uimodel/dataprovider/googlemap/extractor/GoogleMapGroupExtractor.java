/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.dataprovider.googlemap.extractor;

import com.zatech.genesis.customer.portal.biz.common.uimodel.dataprovider.googlemap.result.GoogleMapGroupResult;
import com.zatech.genesis.customer.portal.biz.common.uimodel.dataprovider.googlemap.result.GoogleMapResult;
import com.zatech.genesis.customer.portal.biz.common.uimodel.outer.gateway.GoogleMapService;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.DataProvideContext;

import lombok.val;

import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

import static com.za.cqrs.util.Functions.mapBy;

@Component
public class GoogleMapGroupExtractor implements IGoogleMapExtractor {

    private static final String GROUP_NAME = "groupName";

    @Resource
    private GoogleMapService googleMapService;

    @Override
    public boolean match(GoogleMapExtractTypeEnum extractType) {
        return extractType == GoogleMapExtractTypeEnum.group;
    }

    @Override
    public GoogleMapResult extract(DataProvideContext context, GoogleMapExtractTypeEnum extractType) {
        if (context.getParams().containsKey(GROUP_NAME)) {
            val group = googleMapService.group(context.getParams().get(GROUP_NAME).toString());
            return new GoogleMapGroupResult().setItems(mapBy(group, GoogleMapGroupResult.GroupItem::from));
        }
        return new GoogleMapGroupResult();
    }
}
