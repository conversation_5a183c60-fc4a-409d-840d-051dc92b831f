/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.enums;

import com.zatech.genesis.market.api.structure.response.PackageApplicationElementsEnumResponse;
import com.zatech.genesis.market.api.structure.response.PackageApplicationElementsItemResponse;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.enums.EnumContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.enums.provider.EnumItemsOutput;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.enums.provider.FieldMetaInfo;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.model.EnumItems;
import com.zatech.genesis.portal.toolbox.share.ReflectionSupport;

import java.util.Arrays;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import lombok.val;

import org.apache.commons.lang3.StringUtils;

import static com.zatech.genesis.customer.portal.biz.common.uimodel.EnumConstants.GROUP;
import static java.util.Optional.ofNullable;

@Slf4j
public abstract class AbstractApplicationElementEnumProvider {

    private static final String COUNTRY_CODE = "countryCode";

    /**
     * 从application element配置中获取相关枚举，ui model字段上打注解的时候必须添加group这个参数，对应的值是
     * ApplicationElementItemsResponse中的属性，表示当前字段是那个维度下面的数据。
     * 如果ui model中定义的字段名和application element配置中的code不一样，可以添加code参数进行映射。
     * 匹配配置的时候优先使用注解上面的code参数，如果没有code参数则使用当前字段名匹配
     * 例如：
     *
     * @param fieldKey
     * @param metaInfo
     * @param packageApplicationElementsEnumResponse
     * @param context
     * @EnumProvider(provider = ApplicationElementEnumProvider.class, params = {@Param(key = "group", value = "objectItems"), @Param(key = "code",
     * value = "vehicleBodyType")})
     */
    public EnumItemsOutput provide(String fieldKey, FieldMetaInfo metaInfo, PackageApplicationElementsEnumResponse packageApplicationElementsEnumResponse, EnumContext context) {
        if (Objects.isNull(packageApplicationElementsEnumResponse)) {
            return new EnumItemsOutput(fieldKey, EnumItems.Empty);
        }

        try {
            String applicationElementGroup =
                (String) ofNullable(context.getParams()).map(map -> map.get(GROUP))
                    .orElse(String.valueOf(metaInfo.getAnnotationParams().get(GROUP)));

            val applicationConfig = (List<PackageApplicationElementsItemResponse>) ReflectionSupport.getFieldValue(packageApplicationElementsEnumResponse, applicationElementGroup);
            if (applicationConfig == null) {
                log.warn("Application element config for {} is empty", metaInfo.getAnnotationParams());
                return new EnumItemsOutput(fieldKey, EnumItems.Empty);
            }
            val code = ofNullable(metaInfo.getAnnotationParams().get("code")).orElse(metaInfo.getPropDef().getName()).toString();

            val enumItems = new EnumItems();

            applicationConfig.stream().filter(item -> item.getSchemaFieldCode().equalsIgnoreCase(code))
                .findFirst().ifPresent(item ->
                    item.getItems().stream()
                        .forEach(enumItem -> {
                            if (!COUNTRY_CODE.equalsIgnoreCase(code)) {
                                enumItems.addEnumItem(new com.zatech.genesis.portal.toolbox.share.model.EnumItem(convertEnumName(String.valueOf(enumItem.getValue()), metaInfo),
                                    enumItem.getDisplayName()));
                            } else if (StringUtils.isNotBlank(enumItem.getItemExtend2())) {
                                enumItems.addEnumItem(new com.zatech.genesis.portal.toolbox.share.model.EnumItem(enumItem.getItemExtend2(), enumItem.getItemExtend2()));
                            }
                        }));

            Collection<com.zatech.genesis.portal.toolbox.share.model.EnumItem> values = enumItems.stream()
                .collect(Collectors.toMap(com.zatech.genesis.portal.toolbox.share.model.EnumItem::getCode, Function.identity(), (existing,
                                                                                                                                 replacement) -> existing, LinkedHashMap::new)).values();
            return new EnumItemsOutput(fieldKey, new EnumItems().addEnumItems(values.stream().toList()));
        } catch (Exception ex) {
            log.error("Process dict key: {}, occur error: {}", fieldKey, ex.getMessage());
        }

        return new EnumItemsOutput(fieldKey, EnumItems.Empty);
    }

    private String convertEnumName(String code, FieldMetaInfo metaInfo) {
        try {
            if (metaInfo.getPropDef().getPrimaryType().isEnumType()) {
                Object[] enumConstants = metaInfo.getPropDef().getPrimaryType().getRawClass().getEnumConstants();
                Optional<Object> codeValue = Arrays.stream(enumConstants)
                    .filter(enumItem -> code.equalsIgnoreCase(ReflectionSupport.getFieldValue(enumItem, "code").toString()))
                    .findFirst();
                if (codeValue.isPresent()) {
                    return codeValue.get().toString();
                } else {
                    return code;
                }
            }
        } catch (Exception e) {
            log.warn("Code mapping occur error, code: {}, message: {}", metaInfo.getPropDef().getName(), e.getMessage());
        }
        return code;
    }

}