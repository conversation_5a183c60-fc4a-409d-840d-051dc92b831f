/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.dataprovider.googlemap.extractor;

import com.google.common.collect.Lists;
import com.zatech.genesis.customer.portal.biz.common.uimodel.dataprovider.googlemap.result.GoogleMapCountryResult;
import com.zatech.genesis.customer.portal.biz.common.uimodel.dataprovider.googlemap.result.GoogleMapResult;
import com.zatech.genesis.customer.portal.biz.common.uimodel.outer.gateway.GoogleMapService;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.dataprovide.DataProvideContext;

import java.util.Optional;

import lombok.val;

import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

@Component
public class GoogleMapCountryExtractor implements IGoogleMapExtractor {

    @Resource
    private GoogleMapService googleMapService;

    @Override
    public boolean match(GoogleMapExtractTypeEnum extractType) {
        return extractType == GoogleMapExtractTypeEnum.country;
    }

    @Override
    public GoogleMapResult extract(DataProvideContext context, GoogleMapExtractTypeEnum extractType) {
        val country = googleMapService.queryCountryList();
        val response = new GoogleMapCountryResult();
        val countries = Optional.ofNullable(country.getCountries()).orElseGet(Lists::newArrayList).stream()
            .map(e -> new GoogleMapCountryResult.Country()
                .setName(e.getName())
                .setI18NName(e.getI18NName())
                .setNumericCode(e.getNumericCode())
                .setCode(e.getCode())).toList();
        response.setCountries(countries);
        return response;
    }
}
