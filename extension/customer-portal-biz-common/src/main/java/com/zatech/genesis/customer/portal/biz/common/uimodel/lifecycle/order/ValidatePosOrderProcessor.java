/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.lifecycle.order;

import com.zatech.genesis.customer.portal.biz.common.uimodel.CommonModel;
import com.zatech.genesis.customer.portal.biz.common.uimodel.lifecycle.uimodel.PolicyPermissionCheckService;
import com.zatech.genesis.portal.lowcode.framework.auth.sdk.auth.detail.AbstractAuthenticationDetail;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.lifecycle.IOrderLifecycleProcessor;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.lifecycle.context.OrderLifecycleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.input.CreateOrderExtensionInput;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.input.CreateOrderInput;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.service.input.CreateUIModelInput;

import java.util.Objects;
import java.util.Optional;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

@Component
public class ValidatePosOrderProcessor implements IOrderLifecycleProcessor<CommonModel> {

    @Autowired
    private PolicyPermissionCheckService policyPermissionCheckService;

    @Override
    public int order() {
        return 0;
    }

    @Override
    public void beforeCreateOrder(OrderLifecycleContext context, CreateOrderInput<CommonModel> input) {
        doPolicyCheck(input);
        doExtensionCheck(input);
    }

    private void doExtensionCheck(CreateOrderInput<CommonModel> input) {
        Optional.ofNullable(SecurityContextHolder.getContext()).filter(context -> context.getAuthentication() != null && context.getAuthentication().getDetails() != null ).ifPresent(x -> {
            if (x.getAuthentication().getDetails()  instanceof AbstractAuthenticationDetail abstractAuthenticationDetail) {
                Optional.ofNullable(input.getExtension())
                    .map(CreateOrderExtensionInput::getAgentCode)
                        .ifPresent(a -> {
                            if (Objects.isNull(abstractAuthenticationDetail.getAgentInfo()) || !Objects.equals(a, abstractAuthenticationDetail.getAgentInfo().getSalesCode())) {
                                throw new RuntimeException("Agent code not match");
                            }
                        });
                Optional.ofNullable(input.getExtension())
                    .map(CreateOrderExtensionInput::getBranchCode)
                    .ifPresent(b -> {
                        if (Objects.isNull(abstractAuthenticationDetail.getAgentInfo()) || abstractAuthenticationDetail.getAgentInfo().getAgreements().stream().noneMatch(agreement -> Objects.equals(b, agreement.getTeamCode()))) {
                            throw new RuntimeException("Team code not match");
                        }
                    });
            }
        });

    }

    private void doPolicyCheck(CreateOrderInput<CommonModel> input) {
        Optional.ofNullable(input)
            .map(CreateOrderInput::getUiModel)
            .map(CreateUIModelInput::getData)
            .map(CommonModel::getPolicyNo)
            .ifPresent(x -> policyPermissionCheckService.check(x));
    }

}
