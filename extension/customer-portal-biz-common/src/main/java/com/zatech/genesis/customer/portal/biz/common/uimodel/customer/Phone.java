/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.customer;

import com.zatech.gaia.resource.components.enums.common.PhoneTypeEnum;
import com.zatech.genesis.customer.portal.biz.common.uimodel.DynamicMetaField;
import com.zatech.genesis.customer.portal.biz.common.uimodel.enums.ApplicationElementEnumProvider;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.annotation.Param;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.enums.annotations.EnumProvider;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;

import static com.zatech.genesis.customer.portal.biz.common.uimodel.EnumConstants.GROUP;
import static com.zatech.genesis.customer.portal.biz.common.uimodel.EnumConstants.HOLDER;

@Getter
@Setter
public class Phone extends DynamicMetaField {

    private Long phoneId;

    @Schema(description = "电话号码")
    private String phoneNo;

    /**
     * 电话类型
     */
    @EnumProvider(provider = ApplicationElementEnumProvider.class, params = {@Param(key = GROUP, value = HOLDER)})
    private PhoneTypeEnum phoneType;

    /**
     * 电话国家区号
     */
    @EnumProvider(provider = ApplicationElementEnumProvider.class, params = {@Param(key = GROUP, value = HOLDER)})
    private String countryCode;

}
