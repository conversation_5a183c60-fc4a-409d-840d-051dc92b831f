package com.zatech.genesis.customer.portal.biz.common.uimodel.config;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.zatech.genesis.portal.toolbox.share.function.ThrowingBiFunction;
import com.zatech.genesis.portal.toolbox.share.function.ThrowingFunction;
import com.zatech.genesis.portal.toolbox.share.json.IJsonModule;
import com.zatech.genesis.portal.toolbox.share.json.IJsonParser;
import com.zatech.genesis.portal.toolbox.share.json.exception.JsonParseException;
import com.zatech.genesis.portal.toolbox.share.json.model.JsonMap;
import com.zatech.genesis.portal.toolbox.share.json.model.JsonValue;
import com.zatech.genesis.portal.toolbox.share.json.registry.IObjectMapperCustomizer;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import lombok.Getter;

@Getter
public class JsonParser implements IJsonParser {

    private final ObjectMapper objectMapper = new ObjectMapper();

    {
        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addSerializer(JsonValue.class, new JsonValueSerializer());
        objectMapper.registerModule(simpleModule);
        objectMapper.registerModule(new JavaTimeModule());
        objectMapper.registerModule(new Jdk8Module());
        //不序列化空的map和list，以及null
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        //空的bean不报错
        objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        //不认识的属性不报错
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);

        //允许comments
        objectMapper.configure(com.fasterxml.jackson.core.JsonParser.Feature.ALLOW_COMMENTS, true);

        //考虑到时区问题，不能转为timestamp
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
    }

    @Override
    public void customize(IObjectMapperCustomizer customizer) {

        customizer.customize(objectMapper);
    }

    @Override
    public void registerModule(IJsonModule jsonModule) {

        objectMapper.registerModule(jsonModule.newModule());
    }

    @Override
    public String toJsonString(Object obj) {

        return wrap(objectMapper::writeValueAsString, obj);
    }

    @Override
    public <T> T fromJsonString(String jsonString, Class<T> typeOfT) {

        return wrap(objectMapper::readValue, jsonString, typeOfT);
    }

    @Override
    public <T> T fromJsonString(String jsonString, TypeReference<T> type) {

        return wrap(objectMapper::readValue, jsonString, type);
    }

    @Override
    public JsonMap fromJsonStringToMap(String jsonString) {

        return fromJsonString(jsonString, JsonMap.class);
    }

    @Override
    public <T> List<T> fromJsonStringToList(String jsonString, Class<T> itemClass) {

        return wrap(objectMapper::readValue, jsonString,
                objectMapper.getTypeFactory().constructParametricType(ArrayList.class, itemClass));
    }

    @Override
    public JsonMap fromObjectToMap(Object obj) {

        return wrap(objectMapper::convertValue, obj, JsonMap.class);
    }

    @Override
    public <T> List<T> fromObjectToList(Object obj, Class<T> itemClass) {

        return wrap(objectMapper::convertValue, obj,
                objectMapper.getTypeFactory().constructParametricType(ArrayList.class, itemClass));
    }

    @Override
    public <T> T fromMapToObject(Map<String, Object> map, Class<T> typeOfT) {

        return wrap(objectMapper::convertValue, map, typeOfT);
    }

    @Override
    public <T> T copyObject(Object obj, Class<T> targetCls) {

        return wrap(objectMapper::convertValue, obj, targetCls);
    }

    private static <T, R> R wrap(ThrowingFunction<T, R> functor, T t) {

        try {
            return t == null ? null : functor.delegate(t);
        } catch (Exception e) {
            throw new JsonParseException(e);
        }
    }

    private static <T, U, R> R wrap(ThrowingBiFunction<T, U, R> functor, T t, U u) {

        try {
            return t == null ? null : functor.delegate(t, u);
        } catch (Exception e) {
            throw new JsonParseException(e);
        }
    }

    private static class JsonValueSerializer extends JsonSerializer<JsonValue> {

        @Override
        public void serialize(JsonValue jsonValue, JsonGenerator jsonGenerator, SerializerProvider serializerProvider) throws IOException {

            Object value = jsonValue.getValue();
            if (value == null) {
                jsonGenerator.writeNull();
            } else {
                jsonGenerator.writeObject(value);
            }
        }

    }

}
