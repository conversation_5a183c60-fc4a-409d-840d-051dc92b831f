/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.processer.payment.errorcdoes;

import com.zatech.genesis.portal.toolbox.exception.errorcode.IErrorCode;

public enum PaymentHandleErrorCodes implements IErrorCode {

    CREATE_PAYMENT_ERROR,

    CANCEL_PAYMENT_ERROR;

    @Override
    public String getModuleName() {
        return "payment";
    }

    @Override
    public String getErrorCode() {
        return name();
    }
}
