/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.processer.payment.result;

import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;
import com.zatech.gaia.resource.components.enums.common.TransTypeEnum;
import com.zatech.gaia.resource.components.enums.paymentgateway.PayStatusEnum;
import com.zatech.genesis.customer.portal.integration.outer.openapi.payment.response.PayOrderResp;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.FailureResult;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.SuccessResult;

import java.util.Date;

import lombok.Data;

@Data
public class CancelPaymentResult implements Result {

    private PayStatusEnum payStatus;

    private CurrencyEnum currency;

    private String amount;

    private String payOrderNo;

    private Date payDate;

    private TransTypeEnum payBusinessType;

    private String payBusinessNo;

    private Date applyDate;

    private String errorCode;

    public static CancelPaymentResult from(PayOrderResp cancelPayResp) {
        var cancelPayOutput = new CancelPaymentResult();
        cancelPayOutput.setCurrency(cancelPayResp.getCurrency());
        cancelPayOutput.setAmount(cancelPayResp.getAmount());
        cancelPayOutput.setPayStatus(PayStatusEnum.CANCEL);
        cancelPayOutput.setPayOrderNo(cancelPayResp.getPayOrderNo());
        cancelPayOutput.setPayDate(cancelPayResp.getPayDate());
        cancelPayOutput.setPayBusinessNo(cancelPayResp.getPayBusinessNo());
        cancelPayOutput.setApplyDate(cancelPayResp.getApplyDate());
        cancelPayOutput.setPayBusinessType(cancelPayResp.getPayBusinessType());
        cancelPayOutput.setErrorCode(cancelPayResp.getErrorCode());
        return cancelPayOutput;
    }

    @Override
    public boolean isSuccess() {
        return true;
    }

    @Override
    public SuccessResult succeed() {
        return SuccessResult.from(this);
    }

    @Override
    public FailureResult failed() {
        return null;
    }
}
