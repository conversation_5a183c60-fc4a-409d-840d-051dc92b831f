/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.processer.payment;

import com.zatech.genesis.customer.portal.biz.common.uimodel.processer.payment.param.CancelPayParams;
import com.zatech.genesis.customer.portal.biz.common.uimodel.processer.payment.result.CancelPaymentResult;
import com.zatech.genesis.customer.portal.common.payment.PaymentManager;
import com.zatech.genesis.customer.portal.common.payment.request.CancelPaymentInput;
import com.zatech.genesis.customer.portal.integration.outer.openapi.payment.response.PayOrderResp;
import com.zatech.genesis.customer.portal.integration.outer.payment.PaymentAdapter;
import com.zatech.genesis.payment.gateway.api.request.PayCancelReq;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.BusinessHandleContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.IPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.businesshandle.phase.PaymentPhase;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.phase.IUIModelPhasePostProcessor;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.util.JsonParser;

import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
public class DefaultCancelPaymentPhaseProcessor implements IUIModelPhasePostProcessor<CancelPayParams> {

    @Autowired
    private PaymentAdapter paymentAdapter;

    @Autowired
    private PaymentManager paymentManager;

    @Override
    public IPhase[] forPhases() {
        return new IPhase[] {PaymentPhase.cancel};
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Result process(IPhase iPhase, CancelPayParams cancelPayParams, BusinessHandleContext businessHandleContext) {
        CancelPaymentInput cancelPaymentInput = new CancelPaymentInput();
        cancelPaymentInput.setPayOrderNo(cancelPayParams.getPayOrderNo());
        cancelPaymentInput.setOrderNo(businessHandleContext.getOrder().getOrderNo());
        paymentManager.cancelPay(cancelPaymentInput, businessHandleContext);

        PayCancelReq payCancelReq = buildPaymentOderRequest(cancelPayParams);
        log.info("DefaultCancelPaymentPhaseProcessor.process payCancelReq:{}", JsonParser.toJsonString(payCancelReq));
        PayOrderResp cancelPaymentResult = paymentAdapter.cancel(payCancelReq);
        log.info("DefaultCancelPaymentPhaseProcessor.process cancelPaymentResult:{}", JsonParser.toJsonString(cancelPaymentResult));

        return CancelPaymentResult.from(cancelPaymentResult);
    }

    private PayCancelReq buildPaymentOderRequest(CancelPayParams cancelPayParams) {
        PayCancelReq cancelPayReq = new PayCancelReq();
        cancelPayReq.setPayOrderNo(cancelPayParams.getPayOrderNo());
        cancelPayReq.setExtensionParams(cancelPayParams.getExtensionParams());
        return cancelPayReq;
    }

}
