/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.outer.market;

import com.zatech.genesis.customer.portal.biz.common.uimodel.outer.market.fallback.IOuterCommonMarketServiceFallbackFactory;
import com.zatech.genesis.market.api.structure.request.PackageApplicationElementsEnumRequest;
import com.zatech.genesis.market.api.structure.request.QueryGoodsRelatingRequest;
import com.zatech.genesis.market.api.structure.response.GoodsRelatingResponse;
import com.zatech.genesis.market.api.structure.response.PackageApplicationElementsEnumResponse;

import jakarta.validation.constraints.NotNull;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @Author: zhenbin.li
 */
@FeignClient(name = "zatech-common-market", url = "${genesis-feign-market}", fallbackFactory = IOuterCommonMarketServiceFallbackFactory.class)
public interface IOuterMarketService {

    @PostMapping(value = "/api/v2/market/structure/queryGoodsRelating")
    GoodsRelatingResponse queryGoodsRelating(QueryGoodsRelatingRequest request);

    @PostMapping(value = "/api/v2/market/structure/application-elements/enum-items")
    PackageApplicationElementsEnumResponse queryPackageApplicationElementEnumItem(@RequestBody @NotNull PackageApplicationElementsEnumRequest request);

}