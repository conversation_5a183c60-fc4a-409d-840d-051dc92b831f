/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.outer.gateway;

import com.zatech.genesis.customer.portal.biz.common.uimodel.outer.gateway.request.GoogleCompletionRequest;
import com.zatech.genesis.customer.portal.biz.common.uimodel.outer.gateway.response.GroupItemResponse;
import com.zatech.genesis.gateway.api.googleMap.dto.GoogleCompletionResponse;
import com.zatech.genesis.gateway.api.googleMap.dto.GooglePlaceDetailsResponse;
import com.zatech.genesis.gateway.api.googleMap.dto.MapCityResponse;
import com.zatech.genesis.gateway.api.googleMap.dto.MapCountryResponse;
import com.zatech.octopus.core.util.JacksonUtil;

import java.util.List;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import lombok.val;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class GoogleMapServiceImpl implements GoogleMapService {

    @Autowired
    private IOutMtlGatewayService iOutMtlGatewayService;

    @Override
    public List<GoogleCompletionResponse> queryGoogleAddress(String searchText, String country) {
        log.info("IOuterIntGatewayService searchText: {}", searchText);
        GoogleCompletionRequest completionRequest = new GoogleCompletionRequest();
        completionRequest.setCountry(country);
        completionRequest.setSearchText(searchText);
        val resp = iOutMtlGatewayService.queryGoogleAddressSearch(completionRequest);
        log.info("IOuterIntGatewayService response: {}", resp);
        return resp.stream().map(item -> {
            val out = new GoogleCompletionResponse();
            BeanUtils.copyProperties(item, out);
            return out;
        }).collect(Collectors.toList());
    }

    @Override
    public GooglePlaceDetailsResponse queryAddressDetails(String placeId) {
        log.info("IOuterIntGatewayService.queryGoogleAddress placeId: {}", placeId);
        val resp = iOutMtlGatewayService.queryAddressDetails(placeId);
        log.info("IOuterIntGatewayService.queryGoogleAddress response: {}", resp);
        val out = new GooglePlaceDetailsResponse();
        BeanUtils.copyProperties(resp, out);
        return out;
    }

    @Override
    public List<GroupItemResponse> group(String groupName) {
        log.info("IOuterIntGatewayService group: {}", groupName);
        val resp = iOutMtlGatewayService.group(groupName);
        if (resp.isEmpty()) {
            log.warn("Dont support group:{}", groupName);
        }
        log.info("IOuterIntGatewayService response: {}", resp);
        return resp.stream().map(item -> {
            val out = new GroupItemResponse(item.getCode(), item.getName(), item.getDesc());
            BeanUtils.copyProperties(item, out);
            return out;
        }).collect(Collectors.toList());
    }

    @Override
    public MapCountryResponse queryCountryList() {
        log.info("IOuterIntGatewayService.queryCountryList");
        val resp = iOutMtlGatewayService.queryCountryList();
        log.info("IOuterIntGatewayService.queryCountryList response: {}", JacksonUtil.toJSONString(resp));
        val out = new MapCountryResponse();
        BeanUtils.copyProperties(resp, out);
        return out;
    }

    @Override
    public MapCityResponse queryCityList(String numericCode, String postCode) {
        log.info("IOuterIntGatewayService.queryCityList numericCode:{}, postCode:{}", numericCode, postCode);
        val resp = iOutMtlGatewayService.queryCityList(numericCode, postCode);
        log.info("IOuterIntGatewayService.queryCountryList response: {}", JacksonUtil.toJSONString(resp));
        val out = new MapCityResponse();
        BeanUtils.copyProperties(resp, out);
        return out;
    }
}
