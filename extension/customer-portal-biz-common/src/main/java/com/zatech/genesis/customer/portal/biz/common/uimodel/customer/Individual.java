/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.customer;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zatech.gaia.resource.components.enums.common.CertiTypeEnum;
import com.zatech.gaia.resource.components.enums.common.GenderEnum;
import com.zatech.genesis.customer.portal.biz.common.uimodel.enums.ApplicationElementEnumProvider;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.annotation.Param;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.enums.annotations.EnumProvider;
import com.zatech.genesis.portal.toolbox.encrypt.annotation.Encrypted;

import java.time.LocalDate;
import java.util.Objects;

import lombok.Getter;
import lombok.Setter;

import static com.zatech.genesis.customer.portal.biz.common.uimodel.EnumConstants.CODE;
import static com.zatech.genesis.customer.portal.biz.common.uimodel.EnumConstants.GROUP;
import static com.zatech.genesis.customer.portal.biz.common.uimodel.EnumConstants.HOLDER;

@Getter
@Setter
public class Individual extends BasePersonInfo {

    private LocalDate birthday;

    @JsonProperty("idType")
    @EnumProvider(provider = ApplicationElementEnumProvider.class, params = {@Param(key = GROUP, value = HOLDER), @Param(key = CODE, value = "certiType")})
    private CertiTypeEnum certiType;

    @Encrypted
    @JsonProperty("idValue")
    private String certiNo;

    @Encrypted
    private String firstName;

    @Encrypted
    private String lastName;

    @Encrypted
    private String fullName;

    @Encrypted
    private String middleName;

    private String occupationCode;

    @EnumProvider(provider = ApplicationElementEnumProvider.class, params = {@Param(key = GROUP, value = HOLDER)})
    private GenderEnum gender;

    private Boolean disabilityOrNot;

    public boolean customerCompare(Individual target) {
        if (target == null) {
            return false;
        }

        if (!Objects.equals(getCertiType(), target.getCertiType())) {
            return false;
        }

        if (!Objects.equals(getCertiNo(), target.getCertiNo())) {
            return false;
        }

        if (!Objects.equals(getFirstName(), target.getFirstName())) {
            return false;
        }

        if (!Objects.equals(getLastName(), target.getLastName())) {
            return false;
        }

        return addressesCompare(target);
    }

}

