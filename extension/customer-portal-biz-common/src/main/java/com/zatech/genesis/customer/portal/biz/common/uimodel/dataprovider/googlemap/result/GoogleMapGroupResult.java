/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.dataprovider.googlemap.result;

import com.zatech.genesis.customer.portal.biz.common.uimodel.outer.gateway.response.GroupItemResponse;

import java.util.List;

import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

@Setter
@Getter
@Accessors(chain = true)
public class GoogleMapGroupResult extends GoogleMapResult {

    private List<GroupItem> items;

    @Setter
    @Getter
    @Accessors(chain = true)
    public static class GroupItem {

        private String code;

        private String name;

        private String desc;

        public static GroupItem from(GroupItemResponse response) {
            return new GroupItem().setCode(response.getCode()).setDesc(response.getDesc()).setName(response.getName());
        }
    }

}
