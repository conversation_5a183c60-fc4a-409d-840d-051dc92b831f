package com.zatech.genesis.customer.portal.biz.common.uimodel.config;

import com.zatech.genesis.portal.toolbox.share.json.IJsonParser;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.jackson.JacksonProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

@Configuration
public class JsonParserConfig {

    @Autowired
    private JacksonProperties jacksonProperties;

    @Bean("jsonParser")
    @Order(0)
    public IJsonParser jsonParser() {

        return newJsonParser();
    }

    private IJsonParser newJsonParser() {

        JsonParser defaultJsonParser = new JsonParser();
        defaultJsonParser.getObjectMapper().setTimeZone(jacksonProperties.getTimeZone());
        return defaultJsonParser;
    }
}
