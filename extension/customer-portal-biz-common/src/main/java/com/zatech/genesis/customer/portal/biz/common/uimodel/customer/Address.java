/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.customer;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.zatech.gaia.resource.components.enums.common.AddressTypeEnum;
import com.zatech.gaia.resource.components.enums.customer.CompanyAddressTypeEnum;
import com.zatech.genesis.customer.portal.biz.common.uimodel.DynamicMetaField;
import com.zatech.genesis.customer.portal.biz.common.uimodel.enums.ApplicationElementEnumProvider;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.annotation.Param;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.enums.annotations.EnumProvider;

import java.util.Date;

import lombok.Getter;

import static com.zatech.genesis.customer.portal.biz.common.uimodel.EnumConstants.GROUP;
import static com.zatech.genesis.customer.portal.biz.common.uimodel.EnumConstants.HOLDER;

@Getter
public class Address extends DynamicMetaField {

    private Long addressId;

    @EnumProvider(provider = ApplicationElementEnumProvider.class, params = {@Param(key = GROUP, value = HOLDER)})
    private AddressTypeEnum addressType;

    private CompanyAddressTypeEnum organizationAddressType;

    @EnumProvider(provider = ApplicationElementEnumProvider.class, params = {@Param(key = GROUP, value = HOLDER)})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String address11;

    @EnumProvider(provider = ApplicationElementEnumProvider.class, params = {@Param(key = GROUP, value = HOLDER)})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String address12;

    @EnumProvider(provider = ApplicationElementEnumProvider.class, params = {@Param(key = GROUP, value = HOLDER)})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String address13;

    @EnumProvider(provider = ApplicationElementEnumProvider.class, params = {@Param(key = GROUP, value = HOLDER)})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String address14;

    @EnumProvider(provider = ApplicationElementEnumProvider.class, params = {@Param(key = GROUP, value = HOLDER)})
    private String address15;

    @EnumProvider(provider = ApplicationElementEnumProvider.class, params = {@Param(key = GROUP, value = HOLDER)})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String address16;

    @EnumProvider(provider = ApplicationElementEnumProvider.class, params = {@Param(key = GROUP, value = HOLDER)})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String address21;

    @EnumProvider(provider = ApplicationElementEnumProvider.class, params = {@Param(key = GROUP, value = HOLDER)})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String address22;

    @EnumProvider(provider = ApplicationElementEnumProvider.class, params = {@Param(key = GROUP, value = HOLDER)})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String address23;

    @EnumProvider(provider = ApplicationElementEnumProvider.class, params = {@Param(key = GROUP, value = HOLDER)})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String address24;

    @EnumProvider(provider = ApplicationElementEnumProvider.class, params = {@Param(key = GROUP, value = HOLDER)})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String address25;

    @EnumProvider(provider = ApplicationElementEnumProvider.class, params = {@Param(key = GROUP, value = HOLDER)})
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String address26;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String zipCode;

    private String diffId;

    private Date gmtCreated;

    private Date gmtModified;

}
