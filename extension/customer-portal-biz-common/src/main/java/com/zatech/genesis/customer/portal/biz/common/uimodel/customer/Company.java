/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.customer;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.zatech.gaia.resource.components.enums.schema.OrganizationIDTypeEnum;
import com.zatech.gaia.resource.graphene.customer.InvoiceTypeEnum;
import com.zatech.gaia.resource.graphene.customer.ReceiveInvoiceMethodEnum;

import java.math.BigDecimal;
import java.util.Objects;
import java.util.Optional;

import lombok.Getter;
import lombok.Setter;

import org.apache.commons.lang3.StringUtils;

@Getter
@Setter
public class Company extends BasePersonInfo {

    public static final String LEASING = "Leasing";

    public static final String NON_LEASING_COMPANY = "Non_Leasing_Company";

    public static final String DEFAULT_BRANCH_CODE = "0000000";

    String organizationName;

    OrganizationIDTypeEnum organizationIdType;

    private String organizationIdNo;

    private String organizationSize;

    private ReceiveInvoiceMethodEnum receiveInvoiceMethod;

    private BigDecimal numberOfEmployees;

    private InvoiceTypeEnum invoiceType;

    private String typeOfBusiness;

    private String branchCode;

    public String getBranchCode() {
        if (!Boolean.FALSE.equals(getLeasingFlag().orElse(null))) {
            return branchCode;
        }
        return DEFAULT_BRANCH_CODE;
    }

    @JsonIgnore
    public Optional<Boolean> getLeasingFlag() {

        if (StringUtils.isBlank(typeOfBusiness)) {
            return Optional.empty();
        }
        return Optional.of(Objects.equals(typeOfBusiness, LEASING));
    }

    public boolean customerCompare(Company target) {
        if (target == null) {
            return false;
        }

        if (!Objects.equals(getOrganizationIdNo() , target.getOrganizationIdNo())) {
            return false;
        }

        if (!Objects.equals(getOrganizationIdType() , target.getOrganizationIdType())) {
            return false;
        }

        if (!Objects.equals(getOrganizationName() , target.getOrganizationName())) {
            return false;
        }

        return addressesCompare(target);
    }


}
