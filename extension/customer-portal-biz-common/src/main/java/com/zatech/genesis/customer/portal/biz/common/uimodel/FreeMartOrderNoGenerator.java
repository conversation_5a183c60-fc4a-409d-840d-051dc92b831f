package com.zatech.genesis.customer.portal.biz.common.uimodel;

import com.zatech.genesis.portal.lowcode.framework.domain.IFreeMartOrderNoGenerator;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

@Primary
@Component(value = "FreeMartOrderNoGenerator")
@ConditionalOnProperty(name = "cp.use-openapi", havingValue = "true", matchIfMissing = false)
public class FreeMartOrderNoGenerator implements IFreeMartOrderNoGenerator {

    public static final String ORDER_NUMBER_PREFIX = "ZMART";

    @Override
    public String generate() {
        return OrderNumberGenerator.generateOrderNumber(ORDER_NUMBER_PREFIX);
    }
}
