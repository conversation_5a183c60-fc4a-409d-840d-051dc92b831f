/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.processer.payment.result;

import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;
import com.zatech.gaia.resource.components.enums.paymentgateway.PayStatusEnum;
import com.zatech.genesis.customer.portal.common.payment.response.CreatePaymentOutput;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.FailureResult;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.Result;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.result.SuccessResult;

import io.swagger.v3.oas.annotations.media.Schema;

import java.util.Date;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CreatePaymentResult implements Result {

    @Schema(name = "pay status")
    private PayStatusEnum status;

    @Schema(name = "order no")
    private String orderNo;

    @Schema(name = "third trans no")
    private String thirdPartyTransNo;

    @Schema(name = "third order no")
    private String thirdPartyOrderNo;

    @Schema(name = "redirect url")
    private String redirectUrl;

    @Schema(name = "return html")
    private String returnHtmlStr;

    @Schema(name = "amount")
    private String amount;

    @Schema(name = "currency")
    private CurrencyEnum currency;

    @Schema(name = "pay date")
    private Date payDate;

    @Schema(name = "frontend cancel url")
    private String frontendCancelUrl;

    @Schema(name = "frontend return url")
    private String frontendReturnUrl;

    @Schema(name = "frontend error url")
    private String frontendErrorUrl;

    @Schema(name = "form param, use to redirectUrl")
    private Map<String, Object> formParams;

    @Override
    public boolean isSuccess() {
        return true;
    }

    @Override
    public SuccessResult succeed() {
        return SuccessResult.from(this);
    }

    @Override
    public FailureResult failed() {
        return null;
    }

    public static CreatePaymentResult from(CreatePaymentOutput createPaymentOutput) {
        CreatePaymentResult result = new CreatePaymentResult();
        result.setOrderNo(createPaymentOutput.getOrderNo());
        result.setThirdPartyTransNo(createPaymentOutput.getThirdPartyTransNo());
        result.setThirdPartyOrderNo(createPaymentOutput.getThirdPartyOrderNo());
        result.setRedirectUrl(createPaymentOutput.getRedirectUrl());
        result.setReturnHtmlStr(createPaymentOutput.getReturnHtmlStr());
        result.setAmount(createPaymentOutput.getAmount());
        result.setCurrency(createPaymentOutput.getCurrency());
        result.setPayDate(createPaymentOutput.getPayDate());
        result.setStatus(createPaymentOutput.getStatus());
        result.setFrontendErrorUrl(createPaymentOutput.getFrontendErrorUrl());
        result.setFrontendCancelUrl(createPaymentOutput.getFrontendCancelUrl());
        result.setFrontendReturnUrl(createPaymentOutput.getFrontendReturnUrl());
        result.setFormParams(createPaymentOutput.getFormParams());
        return result;

    }

}
