/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.event;

import com.zatech.gaia.resource.components.enums.common.TransTypeEnum;

import java.util.List;

import lombok.Getter;

import org.springframework.context.ApplicationEvent;

/**
 * @Author: weizhen.kong
 */
@Getter
public class CreateOrderPosCaseEvent extends ApplicationEvent {

    private Long orderId;

    private String caseNo;

    private String policyNo;

    private List<TransTypeEnum> posItemTypes;

    public CreateOrderPosCaseEvent(Long orderId, String caseNo, String policyNo, List<TransTypeEnum> posItemTypes) {
        super(orderId);
        this.orderId = orderId;
        this.caseNo = caseNo;
        this.policyNo = policyNo;
        this.posItemTypes = posItemTypes;
    }

}