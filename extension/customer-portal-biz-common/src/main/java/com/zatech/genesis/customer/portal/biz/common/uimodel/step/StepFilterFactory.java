/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.step;

import com.zatech.genesis.customer.portal.biz.common.uimodel.BaseModel;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import lombok.AllArgsConstructor;

import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

/**
 * @Author: weizhen.kong
 */
@Component
@AllArgsConstructor
public class StepFilterFactory<T extends BaseModel, R extends PageStepResult> {

    private final List<StepFilter<T, R>> filters;

    private final Map<StepCategoryEnum, List<StepFilter<T, R>>> stepFilter = new ConcurrentHashMap<>();

    @PostConstruct
    public void setUp() {
        filters.stream().collect(Collectors.groupingBy(StepFilter::category, LinkedHashMap::new, Collectors.toList())).forEach((key, value) -> stepFilter.put(key, value));
    }

    public List<R> latestSteps(T dataEntry, StepCategoryEnum stepCategory) {
        List<StepFilter<T, R>> stepFilters = stepFilter.get(stepCategory);
        return stepFilters.stream().map(filter -> filter.filter(dataEntry)).collect(Collectors.toList());
    }

}