package com.zatech.genesis.customer.portal.biz.common.uimodel.outer.policy;

import com.zatech.gaia.resource.components.enums.common.YesNoEnum;
import com.zatech.genesis.customer.portal.biz.common.uimodel.outer.policy.fallback.IOuterCommonPolicyServiceFallbackFactory;
import com.zatech.genesis.policy.api.response.IssuanceResponse;
import com.zatech.genesis.policy.api.response.PolicyResponse;
import com.zatech.genesis.policy.api.response.proposal.ProposalDetailResponse;
import com.zatech.genesis.portal.toolbox.share.exception.PortalException;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2023/10/23
 */
@FeignClient(name = "zatech-common-policy", url = "${genesis-feign-policy}", fallbackFactory = IOuterCommonPolicyServiceFallbackFactory.class)
public interface IOuterPolicyService {

    /**
     * query issuance detail by issuance no
     *
     * @param issuanceNo
     * @param options
     * @param temporary
     * @return
     */
    @GetMapping(value = {"/api/v2/issuances/{issuance-no}"})
    IssuanceResponse getIssuance(@PathVariable(value = "issuance-no") String issuanceNo,
                                 @RequestParam(value = "options", required = false) String options,
                                 @RequestParam(value = "temporary", required = false) YesNoEnum temporary) throws PortalException;

    /**
     * query proposal detail by issuance no(more details than getIssuance)
     *
     * @param proposalNo
     * @param temporary
     * @return
     */
    @GetMapping(value = {"/api/v2/proposals/{proposal-no}"})
    ProposalDetailResponse getProposalDetail(@PathVariable(value = "proposal-no") String proposalNo,
                                             @RequestParam(value = "temporary", required = false) YesNoEnum temporary,
                                             @RequestParam(value = "queryCustomer", required = false) Boolean queryCustomer);

    /**
     * query policy info by policyNo
     *
     * @param policyNo
     * @param eventNo
     * @return
     */
    @Operation(summary = "query policy info by policyNo", responses = {
        @ApiResponse(responseCode = "200", content = @Content(mediaType = "application/json", schema = @Schema(implementation = PolicyResponse.class)))
    })
    @GetMapping(value = {"/api/v2/policies/{policyNo}"})
    PolicyResponse queryPolicy(@PathVariable(value = "policyNo") String policyNo,
                               @RequestParam(value = "eventNo", required = false) String eventNo,
                               @RequestParam(value = "queryCustomer", required = false) Boolean queryCustomer);
}
