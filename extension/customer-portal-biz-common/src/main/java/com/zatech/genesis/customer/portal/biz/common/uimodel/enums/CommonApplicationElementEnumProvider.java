/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.enums;

import com.zatech.genesis.customer.portal.biz.common.uimodel.CommonModel;
import com.zatech.genesis.customer.portal.biz.common.uimodel.outer.market.IOuterMarketService;
import com.zatech.genesis.customer.portal.biz.common.uimodel.outer.policy.IOuterPolicyService;
import com.zatech.genesis.market.api.structure.request.PackageApplicationElementsEnumRequest;
import com.zatech.genesis.market.api.structure.response.PackageApplicationElementsEnumResponse;
import com.zatech.genesis.policy.api.response.PolicyResponse;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.enums.EnumContext;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.enums.provider.EnumItemsOutput;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.enums.provider.FieldMetaInfo;

import java.util.HashMap;

import lombok.AllArgsConstructor;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: zhenbin.li
 */
@Component
@AllArgsConstructor
public class CommonApplicationElementEnumProvider extends AbstractApplicationElementEnumProvider implements ApplicationElementEnumProvider<CommonModel> {

    @Autowired
    private IOuterPolicyService outerPolicyService;

    @Autowired
    private IOuterMarketService marketService;

    @Override
    public EnumItemsOutput provide(String fieldKey, FieldMetaInfo metaInfo, CommonModel param, EnumContext context) {
        PackageApplicationElementsEnumRequest packageApplicationElementsEnumRequest = new PackageApplicationElementsEnumRequest();
        packageApplicationElementsEnumRequest.setNeedAssigneeItems(true);
        packageApplicationElementsEnumRequest.setNeedBeneficiaryItems(true);
        packageApplicationElementsEnumRequest.setNeedBeneficialOwnerItems(true);
        packageApplicationElementsEnumRequest.setNeedConsenteeItems(true);
        packageApplicationElementsEnumRequest.setNeedContactPersonItems(true);
        packageApplicationElementsEnumRequest.setNeedHolderItems(true);
        packageApplicationElementsEnumRequest.setNeedInsuredItems(true);
        packageApplicationElementsEnumRequest.setNeedNomineeItems(true);
        packageApplicationElementsEnumRequest.setNeedObjectItems(true);
        packageApplicationElementsEnumRequest.setNeedPayerItems(true);
        packageApplicationElementsEnumRequest.setNeedPolicyItems(true);
        packageApplicationElementsEnumRequest.setNeedPremiumFunderItems(true);
        packageApplicationElementsEnumRequest.setNeedSecondaryLifeInsuredItems(true);
        packageApplicationElementsEnumRequest.setNeedTrusteeItems(true);
        packageApplicationElementsEnumRequest.setPackageId(convert(param, context).getPackageId());
        PackageApplicationElementsEnumResponse packageApplicationElementsEnumResponse = marketService.queryPackageApplicationElementEnumItem(packageApplicationElementsEnumRequest);
        return super.provide(fieldKey, metaInfo, packageApplicationElementsEnumResponse, context);
    }

    public EnumProviderParam convert(CommonModel basicUiModel, EnumContext enumContext) {
        EnumProviderParam enumProviderParam = new EnumProviderParam();
        EnumProviderParam.GoodParam goodParams = new EnumProviderParam.GoodParam();
        PolicyResponse policyResponse = outerPolicyService.queryPolicy(basicUiModel.getPolicyNo(), null, false);

        goodParams.setGoodsId(policyResponse.getGoodsId());
        goodParams.setPlanId(policyResponse.getGoodsPlanId());
        enumProviderParam.setPackageCode(policyResponse.getPackageCode());
        enumProviderParam.setPackageId(policyResponse.getPackageDefId());
        if (enumContext.getParams() == null) {
            enumContext.setParams(new HashMap<>());
        }

        enumProviderParam.setGoods(goodParams);
        return enumProviderParam;
    }

}
