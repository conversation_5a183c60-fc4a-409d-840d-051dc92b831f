/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.biz.common.uimodel.processer.payment.param;

import com.zatech.gaia.resource.components.enums.common.CurrencyEnum;
import com.zatech.gaia.resource.components.enums.paymentgateway.PayChannelEnum;
import com.zatech.gaia.resource.components.enums.paymentgateway.PayMethodEnum;
import com.zatech.genesis.portal.lowcode.framework.client.sdk.util.JsonParser;

import java.math.BigDecimal;
import java.util.Map;

import lombok.Data;

@Data
public class CreatePaymentExtendParams {

    private BigDecimal amount;

    private CurrencyEnum currency;

    private String frontendReturnUrl;

    private String frontendCancelUrl;

    private String frontendErrorUrl;

    private PayChannelEnum payChannel = PayChannelEnum.STRIPE;

    private PayMethodEnum payMethod = PayMethodEnum.CREDIT_CARD;

    private Map<String, Object> extensionParams;

    public static CreatePaymentExtendParams from(Map<String, Object> map) {
        return JsonParser.fromJsonString(JsonParser.toJsonString(map), CreatePaymentExtendParams.class);

    }

}
