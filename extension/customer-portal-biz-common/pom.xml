<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zatech.genesis</groupId>
        <artifactId>customer-portal-extension</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>customer-portal-biz-common</artifactId>
    <packaging>jar</packaging>
    <version>${revision}</version>

    <dependencies>
        <dependency>
            <groupId>com.zatech.genesis.portal</groupId>
            <artifactId>lowcode-framework-client-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis.portal</groupId>
            <artifactId>lowcode-framework-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan.graphene</groupId>
            <artifactId>integration-gateway-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.zhongan.graphene</groupId>
            <artifactId>zatech-market-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.zhongan.graphene</groupId>
            <artifactId>zatech-policy-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis</groupId>
            <artifactId>customer-portal-integration</artifactId>
            <version>${revision}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <configuration>
                    <skip>false</skip>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>flatten-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>flatten</id>
                        <phase>process-resources</phase>
                        <goals>
                            <goal>flatten</goal>
                        </goals>
                        <configuration>
                            <pomElements>
                                <distributionManagement>remove</distributionManagement>
                                <repositories>remove</repositories>
                            </pomElements>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

        </plugins>
    </build>

</project>