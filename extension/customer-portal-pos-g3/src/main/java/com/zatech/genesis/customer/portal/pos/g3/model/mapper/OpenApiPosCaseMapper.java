/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.pos.g3.model.mapper;

import com.zatech.gaia.resource.components.enums.common.TransTypeEnum;
import com.zatech.gaia.resource.components.enums.customer.PartyTypeEnum;
import com.zatech.genesis.customer.portal.biz.common.uimodel.Document;
import com.zatech.genesis.customer.portal.integration.outer.openapi.policy.base.Attachment;
import com.zatech.genesis.customer.portal.integration.outer.openapi.policy.customer.Customer;
import com.zatech.genesis.customer.portal.integration.outer.openapi.policy.customer.PartyIndividual;
import com.zatech.genesis.customer.portal.integration.outer.openapi.policy.customer.party.PartyAddress;
import com.zatech.genesis.customer.portal.integration.outer.openapi.policy.customer.party.PartyEmail;
import com.zatech.genesis.customer.portal.integration.outer.openapi.policy.customer.party.PartyPhone;
import com.zatech.genesis.customer.portal.integration.outer.openapi.pos.model.PosTransaction;
import com.zatech.genesis.customer.portal.pos.g3.model.uimodel.poscase.TravelPosUiModel;
import com.zatech.octopus.framework.mapper.MapStructBaseMapper;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Author: weizhen.kong
 */
@Mapper
public interface OpenApiPosCaseMapper extends MapStructBaseMapper {

    OpenApiPosCaseMapper MAPPER = Mappers.getMapper(OpenApiPosCaseMapper.class);

    /**
     * 将PosApplicationInfo转换为PosTransaction
     *
     * @param posApplicationInfo 保全申请信息
     * @param posTransType       交易类型
     * @return PosTransaction对象
     */
    @Mapping(source = "posTransType", target = "transTypeEnum")
    @Mapping(source = "cancellationType", target = "cancellationType", ignore = true)
    @Mapping(source = "cancellationReason", target = "cancellationReason", ignore = true)
    PosTransaction convertPosApplication(TravelPosUiModel.PosApplicationInfo posApplicationInfo, TransTypeEnum posTransType);

    /**
     * 将Document列表转换为AttachmentInfo列表
     *
     * @param document 文档列表
     * @return 附件信息列表
     */
    @Mapping(source = "code", target = "attachmentUniqueCode")
    @Mapping(source = "docType", target = "documentType")
    @Mapping(source = "docTypeCode", target = "attachmentTypeCode")
    @Mapping(source = "documentName", target = "attachmentName")
    Attachment convertDocument(Document document);

    List<Attachment> convertAttachments(List<Document> documents);

    default Customer convertCustomer(com.zatech.genesis.customer.portal.biz.common.uimodel.customer.Customer customer) {

        if (customer == null) {
            return null;
        }

        com.zatech.genesis.customer.portal.integration.outer.openapi.policy.customer.Customer customer1 =
                new com.zatech.genesis.customer.portal.integration.outer.openapi.policy.customer.Customer();

        Map<String, String> map = customer.getExtensions();
        if (map != null) {
            customer1.setExtensions(new LinkedHashMap<>(map));
        }
        customer1.setCustomerType(customer.getCustomerType());
        customer1.setIndividual(convertIndividual(customer.getIndividual()));
        customer1.setCustomerType(PartyTypeEnum.INDIVIDUAL);
        if (customer1.getIndividual() != null) {
            customer1.getIndividual().setHolderRelation(customer.getRelation());
        }

        return customer1;
    }

    @Mapping(source = "certiNo", target = "certiNo")
    @Mapping(source = "certiType", target = "certiType")
    @Mapping(source = "birthday", target = "birthday")
    @Mapping(source = "gender", target = "sex")
    @Mapping(source = "firstName", target = "firstName")
    @Mapping(source = "middleName", target = "middleName")
    @Mapping(source = "lastName", target = "lastName")
    @Mapping(source = "fullName", target = "fullName")
    @Mapping(source = "emails", target = "emailList")
    @Mapping(source = "phones", target = "phoneList")
    @Mapping(source = "addresses", target = "addressList")
    PartyIndividual convertIndividual(com.zatech.genesis.customer.portal.biz.common.uimodel.customer.Individual individual);

    PartyPhone convertPhone(com.zatech.genesis.customer.portal.biz.common.uimodel.customer.Phone phone);

    PartyEmail convertEmail(com.zatech.genesis.customer.portal.biz.common.uimodel.customer.EmailInfo email);

    default PartyAddress convertAddress(com.zatech.genesis.customer.portal.biz.common.uimodel.customer.Address address) {

        if (address == null) {
            return null;
        }

        PartyAddress partyAddress = new PartyAddress();

        partyAddress.setAddressId(address.getAddressId());
        partyAddress.setAddressType(address.getAddressType());
        partyAddress.setAddress11(Optional.ofNullable(address.getAddress11()).orElse(""));
        partyAddress.setAddress12(Optional.ofNullable(address.getAddress12()).orElse(""));
        partyAddress.setAddress13(Optional.ofNullable(address.getAddress13()).orElse(""));
        partyAddress.setAddress14(Optional.ofNullable(address.getAddress14()).orElse(""));
        partyAddress.setAddress15(Optional.ofNullable(address.getAddress15()).orElse(""));
        partyAddress.setAddress16(Optional.ofNullable(address.getAddress16()).orElse(""));
        partyAddress.setAddress21(Optional.ofNullable(address.getAddress21()).orElse(""));
        partyAddress.setAddress22(Optional.ofNullable(address.getAddress22()).orElse(""));
        partyAddress.setAddress23(Optional.ofNullable(address.getAddress23()).orElse(""));
        partyAddress.setAddress24(Optional.ofNullable(address.getAddress24()).orElse(""));
        partyAddress.setAddress25(Optional.ofNullable(address.getAddress25()).orElse(""));
        partyAddress.setZipCode(address.getZipCode());
        partyAddress.setOrganizationAddressType(address.getOrganizationAddressType());
        return partyAddress;
    }
}