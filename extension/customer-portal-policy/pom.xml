<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zatech.genesis</groupId>
        <artifactId>customer-portal-extension</artifactId>
        <version>${revision}</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <artifactId>customer-portal-policy</artifactId>
    <version>${revision}</version>
    <packaging>jar</packaging>
    <name>customer-portal-policy</name>
    <description>customer portal plugin policy graphene v3</description>

    <dependencies>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>octopus-spring-boot-starter-rpc-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis.portal</groupId>
            <artifactId>toolbox-exception</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>swagger-core-jakarta</artifactId>
                    <groupId>io.swagger.core.v3</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.zhongan</groupId>
            <artifactId>zatech-resourcecode-enum-gaia</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis.portal</groupId>
            <artifactId>lowcode-framework-client-sdk</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis.portal</groupId>
            <artifactId>lowcode-framework-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis.portal</groupId>
            <artifactId>toolbox-jsonschema</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan.pluginframework</groupId>
            <artifactId>plugin-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhongan.pluginframework</groupId>
            <artifactId>plugin-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis</groupId>
            <artifactId>customer-portal-integration</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.zatech.genesis</groupId>
            <artifactId>customer-portal-biz-common</artifactId>
            <version>${revision}</version>
        </dependency>
    </dependencies>

</project>