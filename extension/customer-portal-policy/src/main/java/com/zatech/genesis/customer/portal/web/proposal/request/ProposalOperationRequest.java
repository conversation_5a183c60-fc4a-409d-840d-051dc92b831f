package com.zatech.genesis.customer.portal.web.proposal.request;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "operation")
@JsonSubTypes({
    @JsonSubTypes.Type(value = EditRequest.class, name = "EDIT"),
    @JsonSubTypes.Type(value = WithdrawRequest.class, name = "DEFAULT_WITHDRAW"),
    @JsonSubTypes.Type(value = PayRequest.class, name = "PAY")
})
public interface ProposalOperationRequest {

    String getOperation();

}
