package com.zatech.genesis.customer.portal.web.proposal;

import com.zatech.genesis.customer.portal.web.proposal.request.ProposalOperationRequest;
import com.zatech.genesis.customer.portal.web.proposal.response.ProposalOperationResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import jakarta.validation.Valid;

public interface ProposalOperationApi {

    @PreAuthorize("authenticate() && @checkAuth.checkIssuanceNo(#proposalNo)")
    @PutMapping("/api/proposals/{proposalNo}")
    ProposalOperationResponse modify(
        @PathVariable("proposalNo") String proposalNo,
        @RequestParam(name = "temporary", required = false) Boolean temporary,
        @RequestBody @Valid ProposalOperationRequest request);

}
