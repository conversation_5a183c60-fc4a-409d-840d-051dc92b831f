package com.zatech.genesis.customer.portal.web.proposal;

import java.util.List;

import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;

public interface ProposalDownloadFileApi {

    @PreAuthorize("authenticate() && @checkAuth.checkIssuanceNo(#proposalNo)")
    @GetMapping(value = "/api/proposals/{proposalNo}/files")
    ResponseEntity<ResponseBodyEmitter> batchDownloadProposalFilesAndCompress(
        @PathVariable("proposalNo") String proposalNo,
        @RequestParam(name = "temporary", required = false) Boolean temporary,
        @RequestParam(value = "codes", required = false)  List<String> codes);

    @PreAuthorize("authenticate() && @checkAuth.checkIssuanceNo(#proposalNo)")
    @GetMapping(value = "/api/proposals/{proposalNo}/files/e-offer")
    ResponseEntity<ResponseBodyEmitter> downloadEOfferFile(
        @PathVariable("proposalNo") String proposalNo,
        @RequestParam(name = "temporary", required = false) Boolean temporary);
}
