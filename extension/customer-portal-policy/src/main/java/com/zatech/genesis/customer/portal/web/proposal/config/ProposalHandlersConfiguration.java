package com.zatech.genesis.customer.portal.web.proposal.config;

import com.fasterxml.jackson.databind.jsontype.NamedType;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.zatech.genesis.customer.portal.web.proposal.ProposalOperationHandler;
import com.zatech.genesis.customer.portal.web.proposal.request.ProposalOperationRequest;
import com.zatech.genesis.customer.portal.web.proposal.response.ProposalOperationResponse;
import com.zatech.genesis.portal.lowcode.framework.auth.core.config.JacksonModuleSupplier;
import com.zatech.genesis.portal.toolbox.share.util.TypeUtil;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class ProposalHandlersConfiguration {

    @Bean
    public Map<Class<?>, ProposalOperationHandler<ProposalOperationRequest, ProposalOperationResponse>> proposalHandlers(List<ProposalOperationHandler<?, ?>> handlers) {
        List<Class<?>> requestTypes = new ArrayList<>();
        List<Class<?>> responseTypes = new ArrayList<>();

        Map<Class<?>, ProposalOperationHandler<ProposalOperationRequest, ProposalOperationResponse>> requestType2Handler = new HashMap<>();
        for (var handler : handlers) {
            var requestType = (Class<?>) TypeUtil.findParameterizedTypeOnGenericClass(handler.getClass(), ProposalOperationHandler.class, 0);
            var responseType = (Class<?>) TypeUtil.findParameterizedTypeOnGenericClass(handler.getClass(), ProposalOperationHandler.class, 1);
            requestType2Handler.put(requestType, (ProposalOperationHandler<ProposalOperationRequest, ProposalOperationResponse>) handler);
            requestTypes.add(requestType);
            responseTypes.add(responseType);
        }

        return requestType2Handler;
    }

    @Bean
    public JacksonModuleSupplier proposalTypeModule(List<ProposalOperationHandler<?, ?>> handlers) {
        var module = new SimpleModule();
        handlers.forEach(handler -> {
            var type = (Class<?>) TypeUtil.findParameterizedTypeOnGenericClass(handler.getClass(), ProposalOperationHandler.class, 0);
            module.registerSubtypes(new NamedType(type, getOperation(type)));
        });
        return JacksonModuleSupplier.of(module);
    }

    @SneakyThrows
    private String getOperation(Class<?> clazz) {
        var instance = (ProposalOperationRequest) clazz.getDeclaredConstructor().newInstance();
        return instance.getOperation();
    }

}
