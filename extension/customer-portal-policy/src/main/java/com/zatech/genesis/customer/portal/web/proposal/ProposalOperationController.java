package com.zatech.genesis.customer.portal.web.proposal;

import com.google.common.base.Objects;
import com.zatech.gaia.resource.components.enums.issuance.IssuanceStatusEnum;
import com.zatech.genesis.customer.portal.integration.outer.openapi.IOuterOpenApiService;
import com.zatech.genesis.customer.portal.web.proposal.request.ProposalOperationRequest;
import com.zatech.genesis.customer.portal.web.proposal.response.ProposalOperationResponse;
import com.zatech.genesis.customer.portal.web.proposal.utils.YesNoEnumUtils;
import com.zatech.genesis.portal.toolbox.exception.CommonException;

import java.util.Map;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RequiredArgsConstructor
@RestController
public class ProposalOperationController implements ProposalOperationApi {

    private final Map<Class<?>, ProposalOperationHandler<ProposalOperationRequest, ?>> requestType2Handler;

    private final IOuterOpenApiService policyClient;

    @Override
    public ProposalOperationResponse modify(String proposalNo,
                                            Boolean temporary,
                                            ProposalOperationRequest request) {
        var proposalDetail = policyClient.queryApplication(proposalNo, YesNoEnumUtils.toYesNo(temporary));
        if (proposalDetail == null) {
            log.warn("Null proposal returned (NO: {})", proposalNo);
            throw CommonException.byError(CommonErrorCodes.system_error);
        }

        if (Objects.equal("EDIT", request.getOperation()) &&
            !(Objects.equal(proposalDetail.getProposalStatus(), IssuanceStatusEnum.WAITING_FOR_ISSUANCE) ||
                Objects.equal(proposalDetail.getProposalStatus(), IssuanceStatusEnum.DATA_ENTRY_IN_PROGRESS))) {
            throw CommonException.byError(CommonErrorCodes.system_error);
        }

        if ((Objects.equal("WITHDRAW", request.getOperation()) || Objects.equal("DEFAULT_WITHDRAW", request.getOperation())) &&
            !(Objects.equal(proposalDetail.getProposalStatus(), IssuanceStatusEnum.WAITING_FOR_ISSUANCE) ||
                Objects.equal(proposalDetail.getProposalStatus(), IssuanceStatusEnum.DATA_ENTRY_IN_PROGRESS)||
                Objects.equal(proposalDetail.getProposalStatus(), IssuanceStatusEnum.PENDING_PROPOSAL_CHECK))) {
            throw CommonException.byError(CommonErrorCodes.system_error);
        }

        if (Objects.equal("PAY", request.getOperation()) && !Objects.equal(proposalDetail.getProposalStatus(),
                IssuanceStatusEnum.WAITING_FOR_ISSUANCE)) {
            throw CommonException.byError(CommonErrorCodes.system_error);
        }

        var handler = requestType2Handler.get(request.getClass());
        return handler.handle(proposalNo, temporary, request);
    }

}
