/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.web.proposal.utils;

import com.zatech.gaia.resource.components.enums.common.YesNoEnum;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import static java.util.Optional.ofNullable;

@NoArgsConstructor(access = AccessLevel.NONE)
public class YesNoEnumUtils {

    public static Boolean toBoolean(YesNoEnum yesNoEnum) {
        return ofNullable(yesNoEnum)
            .map(x -> x == YesNoEnum.YES ? Boolean.TRUE : Boolean.FALSE)
            .orElse(null);
    }

    public static YesNoEnum toYesNo(Boolean booleanValue) {
        return ofNullable(booleanValue)
            .map(x -> Boolean.TRUE.equals(x) ? YesNoEnum.YES : YesNoEnum.NO)
            .orElse(null);
    }

    public static YesNoEnum toYesNo(String stringValue) {
        return ofNullable(stringValue)
            .map(YesNoEnum::valueOf)
            .orElse(null);
    }
    public static Boolean toBoolean(String yesNoStr) {
        return ofNullable(yesNoStr)
            .map(x -> "Yes".equals(x) ? Boolean.TRUE : Boolean.FALSE)
            .orElse(null);
    }

}
