package com.zatech.genesis.customer.portal.web.proposal.handler;

import com.zatech.genesis.customer.portal.integration.outer.salesjourney.IOuterSalesJourneyService;
import com.zatech.genesis.customer.portal.integration.outer.salesjourney.request.EditProposalRequest;
import com.zatech.genesis.customer.portal.web.proposal.ProposalOperationHandler;
import com.zatech.genesis.customer.portal.web.proposal.request.PayRequest;
import com.zatech.genesis.customer.portal.web.proposal.response.PayResponse;
import com.zatech.genesis.customer.portal.web.proposal.utils.CopyEditHelper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

@Slf4j
@RequiredArgsConstructor
@Component
public class PayProposalHandler implements ProposalOperationHandler<PayRequest, PayResponse> {

    private final IOuterSalesJourneyService salesJourneyClient;

    private final CopyEditHelper copyEditHelper;

    @Override
    public PayResponse handle(String proposalNo, Boolean temporary, PayRequest request) {
        var sjResponse = salesJourneyClient.editProposal(proposalNo, temporary, new EditProposalRequest());
        return copyEditHelper.buildFromSalesJourneyResponse(sjResponse, new PayResponse());

    }
}
