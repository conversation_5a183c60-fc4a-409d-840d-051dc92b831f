package com.zatech.genesis.customer.portal.web.proposal.utils;

import com.zatech.genesis.customer.portal.integration.outer.salesjourney.IOuterSalesJourneyService;
import com.zatech.genesis.customer.portal.integration.outer.salesjourney.response.BaseCopyEditResponse;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

@Slf4j
@RequiredArgsConstructor
@Service
public class CopyEditHelper {

    private final IOuterSalesJourneyService salesJourneyClient;

    public <T extends com.zatech.genesis.customer.portal.web.proposal.response.BaseCopyEditResponse> T buildFromSalesJourneyResponse(BaseCopyEditResponse sjResponse,
                                                                            T response) {
        response.setOrderNo(sjResponse.getOrderNo());
        response.setStep(sjResponse.getStep());

        var uiModelResponse = salesJourneyClient.queryOrderUiModel(sjResponse.getOrderNo());
        response.setId(uiModelResponse.getId());
        response.setIdentity(uiModelResponse.getIdentity());
        response.setData(uiModelResponse.getData());
        response.setVersion(uiModelResponse.getVersion());
        response.setExtraInfo(uiModelResponse.getExtraInfo());
        response.setLockVersion(uiModelResponse.getLockVersion());
        response.setZoneId(uiModelResponse.getZoneId());
        return response;
    }

}
