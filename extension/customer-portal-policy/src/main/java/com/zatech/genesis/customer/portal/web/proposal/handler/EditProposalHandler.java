package com.zatech.genesis.customer.portal.web.proposal.handler;

import com.zatech.genesis.customer.portal.integration.outer.salesjourney.IOuterSalesJourneyService;
import com.zatech.genesis.customer.portal.integration.outer.salesjourney.request.EditProposalRequest;
import com.zatech.genesis.customer.portal.web.proposal.ProposalOperationHandler;
import com.zatech.genesis.customer.portal.web.proposal.request.EditRequest;
import com.zatech.genesis.customer.portal.web.proposal.response.EditResponse;
import com.zatech.genesis.customer.portal.web.proposal.utils.CopyEditHelper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

@Slf4j
@RequiredArgsConstructor
@Component
public class EditProposalHandler implements ProposalOperationHandler<EditRequest, EditResponse> {

    private final IOuterSalesJourneyService salesJourneyClient;

    private final CopyEditHelper copyEditHelper;

    @Override
    public EditResponse handle(String proposalNo, Boolean temporary, EditRequest request) {
        var sjResponse = salesJourneyClient.editProposal(proposalNo, temporary, new EditProposalRequest());
        return copyEditHelper.buildFromSalesJourneyResponse(sjResponse, new EditResponse());
    }

}
