package com.zatech.genesis.customer.portal.web.proposal;

import com.zatech.genesis.customer.portal.web.proposal.request.ProposalOperationRequest;
import com.zatech.genesis.customer.portal.web.proposal.response.ProposalOperationResponse;

public interface ProposalOperationHandler<T extends ProposalOperationRequest, U extends ProposalOperationResponse> {

    U handle(String proposalNo, Boolean temporary, T request);

}
