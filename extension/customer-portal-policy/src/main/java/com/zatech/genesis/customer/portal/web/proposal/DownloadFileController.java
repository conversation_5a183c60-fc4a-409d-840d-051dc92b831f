package com.zatech.genesis.customer.portal.web.proposal;

import com.zatech.genesis.customer.portal.integration.outer.notificaition.IOuterNotificationService;
import com.zatech.genesis.customer.portal.integration.outer.notificaition.response.NotificationDocFileResponse;
import com.zatech.genesis.customer.portal.integration.outer.openapi.IOuterOpenApiService;
import com.zatech.genesis.customer.portal.integration.outer.openapi.policy.Policy;
import com.zatech.genesis.customer.portal.integration.outer.openapi.policy.base.Attachment;
import com.zatech.genesis.customer.portal.web.proposal.utils.YesNoEnumUtils;
import com.zatech.genesis.portal.toolbox.exception.CommonException;

import java.io.IOException;
import java.io.InputStream;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.io.ByteArrayOutputStream;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import org.springframework.core.io.ClassPathResource;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;

@Slf4j
@RequiredArgsConstructor
@RestController
public class DownloadFileController implements ProposalDownloadFileApi {

    @Autowired
    private IOuterOpenApiService openApiService;

    @Autowired
    private IOuterNotificationService notificationClient;


    @Override
    public ResponseEntity<ResponseBodyEmitter> batchDownloadProposalFilesAndCompress(String proposalNo, Boolean temporary, List<String> codes) {
        Policy proposalDetail = openApiService.queryApplication(proposalNo, YesNoEnumUtils.toYesNo(temporary));
        if (proposalDetail == null) {
            log.warn("Null proposal returned (NO: {})", proposalNo);
            throw CommonException.byError(CommonErrorCodes.system_error);
        }
        if (CollectionUtils.isNotEmpty(codes)) {
            List<String> availableFileCodes = Optional.ofNullable(proposalDetail.getAttachmentList())
                .orElse(Collections.emptyList())
                .stream()
                .map(Attachment::getAttachmentUniqueCode)
                .filter(Objects::nonNull)
                .toList();
            validateRequestedFileCodes(proposalNo, codes, availableFileCodes);
        }
        return batchDownloadFilesAndCompress(proposalNo, codes);
    }

    //todo
    @Override
    public ResponseEntity<ResponseBodyEmitter> downloadEOfferFile(String proposalNo, Boolean temporary) {
        var emitter = new ResponseBodyEmitter();
        
        new Thread(() -> {
            try {
                ClassPathResource resource = new ClassPathResource("e_offer.pdf");
                try (InputStream inputStream = resource.getInputStream()) {
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        byte[] data = new byte[bytesRead];
                        System.arraycopy(buffer, 0, data, 0, bytesRead);
                        emitter.send(data);
                    }
                    emitter.complete();
                } catch (IOException e) {
                    log.error("Error reading e_offer.pdf from resources", e);
                    emitter.completeWithError(e);
                }
            } catch (Exception e) {
                log.error("Error downloading e_offer.pdf", e);
                emitter.completeWithError(e);
            }
        }).start();
        
        return ResponseEntity
            .ok()
            .contentType(MediaType.APPLICATION_PDF)
            .header(HttpHeaders.CONTENT_DISPOSITION, "inline;filename=e_offer.pdf")
            .body(emitter);
    }

    private ResponseEntity<ResponseBodyEmitter> batchDownloadFilesAndCompress(String businessNo, List<String> codes) {
        var emitter = new ResponseBodyEmitter();
        
        if (CollectionUtils.isEmpty(codes)) {
            downloadDefaultFileAsZip(emitter);
        } else {
            // TODO: Implement actual file download and compression logic for non-empty codes
            log.warn("File download for specific codes not yet implemented");
            throw CommonException.byError(CommonErrorCodes.system_error);
        }
        
        return ResponseEntity
            .ok()
            .contentType(new MediaType("application", "zip"))
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + businessNo + ".zip")
            .body(emitter);
    }

    private void downloadDefaultFileAsZip(ResponseBodyEmitter emitter) {
        new Thread(() -> {
            try (ByteArrayOutputStream zipOutputStream = new ByteArrayOutputStream();
                 ZipOutputStream zos = new ZipOutputStream(zipOutputStream)) {
                
                ClassPathResource resource = new ClassPathResource("e_offer.pdf.pdf");
                try (InputStream inputStream = resource.getInputStream()) {
                    
                    ZipEntry zipEntry = new ZipEntry("e_offer.pdf.pdf");
                    zos.putNextEntry(zipEntry);
                    
                    byte[] buffer = new byte[1024];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        zos.write(buffer, 0, bytesRead);
                    }
                    
                    zos.closeEntry();
                    zos.finish();
                    
                    emitter.send(zipOutputStream.toByteArray());
                    emitter.complete();
                    
                } catch (IOException e) {
                    log.error("Error reading e_offer.pdf.pdf from resources", e);
                    emitter.completeWithError(e);
                }
                
            } catch (IOException e) {
                log.error("Error creating ZIP file", e);
                emitter.completeWithError(e);
            }
        }).start();
    }


    private void validateRequestedFileCodes(String transactionId, List<String> requestedCodes, List<String> clauseFileUniqueCodes) {
        Set<String> fileCodes = new HashSet<>();
        List<NotificationDocFileResponse> docFileResponses = notificationClient.queryDocFileByTransactionId(transactionId, null, false, true);
        if (CollectionUtils.isNotEmpty(docFileResponses)) {
            docFileResponses.stream()
                .map(NotificationDocFileResponse::getFileUniqueCode)
                .filter(Objects::nonNull)
                .forEach(fileCodes::add);
        }
        clauseFileUniqueCodes.stream()
            .filter(Objects::nonNull)
            .forEach(fileCodes::add);
        boolean allCodesValid = fileCodes.containsAll(requestedCodes);
        if (!allCodesValid) {
            log.warn("Not Auth File Code");
            throw CommonException.byError(CommonErrorCodes.system_error);
        }
    }
}
