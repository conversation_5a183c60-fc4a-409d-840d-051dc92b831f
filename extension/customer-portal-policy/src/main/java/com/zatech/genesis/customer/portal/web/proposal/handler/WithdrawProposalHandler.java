package com.zatech.genesis.customer.portal.web.proposal.handler;

import com.zatech.genesis.customer.portal.integration.outer.openapi.IOuterOpenApiService;
import com.zatech.genesis.customer.portal.integration.outer.openapi.policy.output.WithdrawApplicationOutput;
import com.zatech.genesis.customer.portal.web.proposal.ProposalOperationHandler;
import com.zatech.genesis.customer.portal.web.proposal.request.WithdrawRequest;
import com.zatech.genesis.customer.portal.web.proposal.response.WithdrawResponse;
import com.zatech.genesis.customer.portal.web.proposal.utils.YesNoEnumUtils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

@Slf4j
@RequiredArgsConstructor
@Component
public class WithdrawProposalHandler implements ProposalOperationHandler<WithdrawRequest, WithdrawResponse> {

    private static final int MAX_RETRY = 8;

    private final IOuterOpenApiService openApiService;

    @Override
    public WithdrawResponse handle(String proposalNo, Boolean temporary, WithdrawRequest request) {
        WithdrawApplicationOutput withdrawApplicationOutput = openApiService.withdrawApplication(proposalNo, YesNoEnumUtils.toYesNo(temporary));

        var ret = new WithdrawResponse();
        ret.setProposalStatus(withdrawApplicationOutput.getProposalStatus());

        return ret;
    }

}
