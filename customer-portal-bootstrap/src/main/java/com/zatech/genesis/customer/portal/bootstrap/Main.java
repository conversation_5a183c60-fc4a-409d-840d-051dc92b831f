/*
 * Copyright By ZATI
 * Copyright By 3a3c88295d37870dfd3b25056092d1a9209824b256c341f2cdc296437f671617
 * All rights reserved.
 *
 * If you are not the intended user, you are hereby notified that any use, disclosure, copying, printing, forwarding or
 * dissemination of this property is strictly prohibited. If you have got this file in error, delete it from your system.
 */

package com.zatech.genesis.customer.portal.bootstrap;

import com.zatech.genesis.customer.portal.common.exception.CustomerCenterErrorCodes;
import com.zatech.genesis.customer.portal.share.ErrorCodeKey;
import com.zatech.genesis.customer.portal.web.WebErrorCodes;
import com.zatech.genesis.portal.lowcode.framework.core.EnableFreeMartFramework;
import com.zatech.genesis.portal.toolbox.exception.EnablePortalToolboxException;
import com.zatech.genesis.portal.toolbox.i18n.I18nBundler;
import com.zatech.genesis.portal.toolbox.i18n.I18nBundlerInitializer;
import com.zatech.genesis.portal.toolbox.i18n.IMessageSourceLookupRegistryInitializer;
import com.zatech.genesis.portal.toolbox.i18n.messagesource.AbstractMessageSourceLookup;
import com.zatech.genesis.portal.toolbox.i18n.messagesource.DefaultMessageSourceCoordinate;
import com.zatech.genesis.portal.toolbox.i18n.messagesource.IMessageSourceLookupContext;
import com.zatech.genesis.portal.toolbox.i18n.messagesource.IMessageSourceLookupRegistry;
import com.zatech.genesis.portal.toolbox.util.EnablePortalToolboxUtil;
import com.zhongan.pluginframework.annotation.EnablePlugin;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * <AUTHOR>
 * @create 2023/9/21 17:45
 **/
@SpringBootApplication(scanBasePackages = {"com.zatech.genesis.customer.portal", "com.zatech.genesis", "com.zatech.gaia", "com.zatech.octopus.framework"})
@EnableCaching
@EnableAspectJAutoProxy
@EnableScheduling
@EnablePortalToolboxUtil
@EnablePortalToolboxException(serviceKey = ErrorCodeKey.KEY, errorCodes = {WebErrorCodes.class, CustomerCenterErrorCodes.class})
@EnableFreeMartFramework
@EnablePlugin(basePackages = {"com.zatech.genesis.customer.portal.plugin", "com.zatech.genesis.portal.lowcode.framework.client"})
@EntityScan(basePackages = "com.zatech.genesis.customer.portal.infra")
@EnableJpaRepositories(basePackages = "com.zatech.genesis.customer.portal.infra")
public class Main implements IMessageSourceLookupRegistryInitializer, I18nBundlerInitializer {

    @Override
    public void initialize(I18nBundler bundler) {
        bundler.addMessageSourceBundleByCoordinate(
            new DefaultMessageSourceCoordinate("i18n/exception/customerportal",
                new String[]{"classpath:i18n/exception/customerportal"}));
    }

    @Override
    public void initialize(IMessageSourceLookupRegistry registry) {
        registry.register(new AbstractMessageSourceLookup<IMessageSourceLookupContext>() {
            @Override
            public String lookupKey(IMessageSourceLookupContext context) {
                return "i18n/exception/customerportal";
            }

            @Override
            public int order() {
                return 0;
            }
        });
    }

    public static void main(String[] args) {
        SpringApplication.run(Main.class, args);
    }
}
