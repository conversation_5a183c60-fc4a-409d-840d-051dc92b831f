cp.cc.verification.nonce.absent=æªæä¾ææåæ°
cp.cc.captcha.code.expired=éªè¯ç å·²è¿æã
cp.cc.verification.code.expired=éªè¯ç å·²è¿æï¼ä»æ¥æå¤å¯è·å5æ¬¡éªè¯ç ã
cp.cc.invalid.captcha.code=éªè¯ç ä¸æ­£ç¡®ã
cp.cc.invalid.mobile=è¯·è¾å¥æ­£ç¡®çææºå·ã
cp.cc.invalid.email=è¯·è¾å¥æ­£ç¡®çé®ç®±ã
cp.cc.operation.frequency=æä½é¢ç¹,è¯·ç¨åå°è¯ã
cp.cc.verification.send.times=ä»å¤©åéçéªè¯ç æ°éå·²è¾¾ä¸éã è¯·æå¤©åè¯ï¼
cp.cc.sms.service.busy=ç­ä¿¡æå¡å¿ï¼è¯·ç¨åå°è¯ã
system.request.param.invalid=è¯·æ±åæ°æ æã
cp.cc.invalid.verification.code=æ æçéªè¯ç ã
cp.cc.notification.rule.not.found=æªæ¾å°éç¥è§åã
cp.cc.mobile.registered=è¯¥ææºå·ç å·²è¢«æ³¨åã
cp.cc.email.registered=é®ç®±å·²æ³¨åã
cp.cc.account.cancel=è´¦å·å·²ä½åºã
cp.cc.account.frozen=è´¦å·å·²å»ç»ã
cp.cc.invalid.password=å¯ç éè¯¯ã
cp.cc.password.not.initialized=è¯·åè®¾ç½®å¯ç ã
cp.cc.password.not.match=è¾å¥çå¯ç ä¸åå¯ç ä¸åï¼è¯·æ£æ¥ã
cp.cc.password.match=ä¿®æ¹åçå¯ç ä¸è½ä¸åå¯ç ç¸åã
cp.cc.channel.customer.not.found=è´¦æ·ä¸å­å¨ã
cp.cc.channel.customer.info.absent=ç¼ºå°è´¦æ·ä¿¡æ¯ã
cp.cc.channel.customer.present=è´¦æ·å·²å­å¨ï¼è¯·ç¡®è®¤è´¦æ·ä¿¡æ¯ã
cp.cc.email.match=ä¿®æ¹ååçé®ç®±ä¸è´ï¼è¯·æ£æ¥ã
cp.cc.mobile.match=ä¿®æ¹ååçææºå·ä¸è´ï¼è¯·æ£æ¥ã
cp.cc.change.password.failure=å¯ç ä¿®æ¹å¤±è´¥ã
cp.cc.reset.password.failure=å¯ç éç½®å¤±è´¥ã
cp.cc.change.email.failure=ä¿®æ¹é®ç®±å¤±è´¥ã
cp.cc.change.mobile.failure=ä¿®æ¹ææºå·ç å¤±è´¥ã
cp.cc.account.been.locked=è´¦å·å·²å»ç»ã
cp.cc.user.identity.incomplete=ç¨æ·çå¯ä¸è¦ç´ ä¿¡æ¯ä¸å®æ´ã
cp.cc.primary.channel.customer.not.found=ä¸»è´¦å·ä¸å­å¨ã
cp.cc.user.existing=ç¨æ·å·²å­å¨ï¼è¯·ç¡®è®¤ç¨æ·ä¿¡æ¯ã
cp.cc.user.element.not.match=ç¨æ·ä¿¡æ¯ä¸å¹éã
cp.cc.channel.customers.not.found=è´¦å·ä¸å­å¨ã
cp.cc.user.not.found=ç¨æ·æªæ¾å°ã
web.authentication.failed=è®¤è¯å¤±è´¥ï¼è¯·éæ°ç»å½ã
integration.user.unique.element.missing=ç¼ºå°å¯ä¸çè¦ç´ éç½®ã
cp.pos.portal.application.element.config.empty=æå¡è¿æªå®æå¯¹äº {0} çéç½®ã
cp.pp.pos.portal.item.configure.not.found=æ²¡ææ¾å°ä¿å¨é¡¹éç½®ã
cp.pp.pos.service.unavailable=å¯¹ä¸èµ·ãç³»ç»éè¯¯ï¼è¯·èç³»æä»¬æäº¤ç³è¯·ã
cp.pp.step.filter.not.impl=è³å°è¦å®ç°ä¸ä¸ªstep filterã
cp.service.order.pay.not.success=ç®åçä»æ¬¾è¿æ²¡æå®æè¯·éæ°å¯å¨ä»æ¬¾ï¼ç¶åæäº¤ç³è¯·ã
cp.service.pos.not.allow=è¯·èç³»ä¿é©å¬å¸è¿è¡ä¿¡æ¯åæ´ã
posStatus.DATA_ENTRY_IN_PROGRESS=æ°æ®å½å¥ä¸­
posStatus.WAITING_FOR_APPROVAL=ç­å¾å®¡æ¹
posStatus.APPROVAL_IN_PROGRESS=å®¡æ¹ä¸­
posStatus.WAITING_FOR_COLLECTION=ç­å¾æ¶é
posStatus.WAITING_FOR_EFFECTIVE=ç­å¾çæ
posStatus.WAITING_FOR_DATA_ENTRY=ç­å¾æ°æ®å½å¥
posStatus.EFFECTIVE=çæ
posStatus.CANCELLED=å·²åæ¶
posStatus.WITHDRAW=å·²æ¤å
posStatus.INVALID=å·²å¤±æ
posStatus.REJECTED=å·²æç»