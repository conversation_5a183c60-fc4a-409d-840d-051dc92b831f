cp.cc.verification.nonce.absent=No valid parameter provided.
cp.cc.captcha.code.expired=Captcha code has expired.
cp.cc.verification.code.expired=Code has expired. You can acquire verification code maximum 5 times today.
cp.cc.invalid.captcha.code=Invalid captcha code.
cp.cc.invalid.mobile=Please input correct phone.
cp.cc.invalid.email=Please input correct E-mail address.
cp.cc.operation.frequency=Operation is frequent, please try later.
cp.cc.verification.send.times=The number of verification codes sent today has reached the limit. Please try again tomorrow!
cp.cc.sms.service.busy=SMS service is busy, please try later.
system.request.param.invalid=Request param invalid.
cp.cc.invalid.verification.code=Invalid verification code.
cp.cc.notification.rule.not.found=Notification rule not found.
cp.cc.mobile.registered=This Mobile is already registered.
cp.cc.email.registered=This E-mail is already registered.
cp.cc.account.cancel=Account has been cancelled.
cp.cc.account.frozen=Account has been frozen.
cp.cc.invalid.password=Incorrect password.
cp.cc.password.not.initialized=Please set a password first.
cp.cc.password.not.match=The password entered is different from the original password, please check.
cp.cc.password.match=The modified password cannot be the same as the original password.
cp.cc.channel.customer.not.found=User account does not exist.
cp.cc.channel.customer.info.absent=Lack of user account information.
cp.cc.channel.customer.present=The user account has been created, please confirm the user account information.
cp.cc.email.match=The email before and after modification is the same, please check.
cp.cc.mobile.match=The mobile phone before and after modification is the same, please check.
cp.cc.change.password.failure=Password modification failed.
cp.cc.reset.password.failure=Password reset failed.
cp.cc.change.email.failure=Email modification failed.
cp.cc.change.mobile.failure=Mobile phone modification failed.
cp.cc.account.been.locked=The account is locked.
cp.cc.user.identity.incomplete=User unique element information is incomplete.
cp.cc.primary.channel.customer.not.found=Primary user account does not exist.
cp.cc.user.existing=User has been existing, please Confirm user info.
cp.cc.user.element.not.match=User information does not match.
cp.cc.channel.customers.not.found=User account does not exist.
cp.cc.user.not.found=User not found.
web.authentication.failed=Authentication failed. Please log in again.
integration.user.unique.element.missing=The unique element configuration is missing.
cp.cc.web.file.upload.error=Server is currently unavailable, resulting in a failed file upload. Please feel free to attempt uploading again.
cp.pos.portal.application.element.config.empty=Application element config not found for {0}
cp.pp.pos.portal.item.configure.not.found=Pos item config is empty.
cp.pp.pos.service.unavailable=Sorry. System error, please contact us to submit application.
cp.pp.step.filter.not.impl=Implement at least a step filter.
cp.service.order.pay.not.success=The current payment has not been completed. Please re-initiate the payment and then submit the application.
cp.service.claim.not.allow=This feature is currently not supported. Please contact your agent or customer service team for further assistance.
cp.openapi.policy.not.found=Policy not found.
cp.integration.e.policy.generating.error=E-policy is still generating, please try again after 5 minutes.
cp.integration.e.policy.generated.error=E-policy failed to generate, please try again later.
cp.service.pos.not.allow=Please contact the insurer for information change.
web.access.forbidden = Access forbidden.

cp.service.pending.case.invalid=Sorry, this URL already expired. If any question, please contact customer service.
cp.service.lack.token=Please check your request, lack token.

posStatus.DATA_ENTRY_IN_PROGRESS=Data entry in progress
posStatus.WAITING_FOR_APPROVAL=Waiting for approval
posStatus.APPROVAL_IN_PROGRESS=Approval in progress
posStatus.WAITING_FOR_COLLECTION=Waiting for collection
posStatus.WAITING_FOR_EFFECTIVE=Waiting for effective
posStatus.WAITING_FOR_DATA_ENTRY=Waiting for data entry
posStatus.EFFECTIVE=Effective
posStatus.CANCELLED=Cancelled
posStatus.WITHDRAW=Withdraw
posStatus.INVALID=Invalid
posStatus.REJECTED=Rejected
ap.common.system.error=System error, please contact IT service.