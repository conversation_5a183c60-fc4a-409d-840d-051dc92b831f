cp.cc.verification.nonce.absent=æå¹ãªãã©ã¡ã¼ã¿ãæä¾ããã¦ãã¾ããã
cp.cc.captcha.code.expired=èªè¨¼ã³ã¼ãã®æå¹æéãåãã¾ããã
cp.cc.verification.code.expired=èªè¨¼ã³ã¼ããèª¤ã£ã¦ãã¾ããèªè¨¼ã³ã¼ãããç¢ºèªã®ä¸ãååº¦å¥åãã¦ãã ããã
cp.cc.invalid.captcha.code=ç¡å¹ãªèªè¨¼ã³ã¼ãã
cp.cc.invalid.mobile=æ­£ããæºå¸¯çªå·ããå¥åãã ããã
cp.cc.invalid.email=æ­£ããã¡ã¼ã«ã¢ãã¬ã¹ãå¥åãã¦ãã ããã
cp.cc.operation.frequency=ãã°ãããå¾ã¡ãã¦ããååº¦ãè©¦ããã ããã
cp.cc.verification.send.times=èªè¨¼ã³ã¼ãã®éä¿¡åæ°ãä¸éã«éãã¾ãããææ¥ä»¥éãååº¦æç¶ãããé¡ããããã¾ãã
cp.cc.sms.service.busy=SMSãè¾¼ãã§ãã¾ãã®ã§ããã°ãããå¾ã¡ãã¦ããååº¦ãè©¦ããã ããã
system.request.param.invalid=è¦æ±ãã©ã¡ã¼ã¿ã¯ç¡å¹ã§ãã
cp.cc.invalid.verification.code=èªè¨¼ã³ã¼ããèª¤ã£ã¦ãã¾ããèªè¨¼ã³ã¼ãããç¢ºèªã®ä¸ãååº¦å¥åãã¦ãã ããã
cp.cc.notification.rule.not.found=éç¥ã«ã¼ã«ãè¦ã¤ããã¾ããã
cp.cc.mobile.registered=ãã®æºå¸¯é»è©±çªå·ã¯æ¢ã«ç»é²ããã¦ããã
cp.cc.email.registered=ãæå®ã®ã¡ã¼ã«ã¢ãã¬ã¹ã¯ãæ¢ã«ä»ã®ã¢ã«ã¦ã³ãã§ç»é²ããã¦ããããå©ç¨ããã ãã¾ããã
cp.cc.account.cancel=ã¢ã«ã¦ã³ãã¯ã­ã£ã³ã»ã«ããã¾ããã
cp.cc.account.frozen=ã¢ã«ã¦ã³ããåçµããã¾ããã
cp.cc.invalid.password=ã­ã°ã¤ã³ã¡ã¼ã«ã¢ãã¬ã¹ã¾ãã¯ãã¹ã¯ã¼ããèª¤ã£ã¦ãã¾ãããç¢ºèªã®ä¸ãååº¦å¥åãã¦ãã ããã
cp.cc.password.not.initialized=ãåã«ãã¹ã¯ã¼ããè¨­å®ãã¦ãã ããã
cp.cc.password.not.match=å¤æ´åã®ãã¹ã¯ã¼ããä¸æ­£ã§ããç¢ºèªãã¦ãã ããã
cp.cc.password.match=å¤æ´å¾ã®ãã¹ã¯ã¼ããå¤æ´åã®ãã¹ã¯ã¼ãã¨åãã§ããå¥ååå®¹ãååº¦ãç¢ºèªãã ããã
cp.cc.channel.customer.not.found=ãå¥åã®ã¡ã¼ã«ã¢ãã¬ã¹ã«å¯¾å¿ããã¢ã«ã¦ã³ããè¦ã¤ããã¾ããããç¢ºèªã®ä¸ãååº¦å¥åãã¦ãã ããã
cp.cc.channel.customer.info.absent=ã¢ã«ã¦ã³ãæå ±ãä¸è¶³ãã¦ãã¾ãã
cp.cc.channel.customer.present=ã¢ã«ã¦ã³ããããã¾ã,ã¢ã«ã¦ã³ãæå ±ãç¢ºèªãã¦ãã ããã
cp.cc.email.match=å¤æ´å¾ã®ã¡ã¼ã«ã¢ãã¬ã¹ãå¤æ´åã®ã¡ã¼ã«ã¢ãã¬ã¹ã¨åãã§ããå¥ååå®¹ãååº¦ãç¢ºèªãã ããã
cp.cc.mobile.match=å¤æ´åã¨å¤æ´å¾ã®æºå¸¯é»è©±çªå·ãåããªã®ã§ãç¢ºèªãã¦ãã ããã
cp.cc.change.password.failure=ãã¹ã¯ã¼ãã®å¤æ´ã«å¤±æãã¾ããã
cp.cc.reset.password.failure=ãã¹ã¯ã¼ãã®ãªã»ããã«å¤±æãã¾ããã
cp.cc.change.email.failure=ã¡ã¼ã«ã®å¤æ´ã«å¤±æãã¾ããã
cp.cc.change.mobile.failure=é»è©±çªå·ã®å¤æ´ã«å¤±æãã¾ããã
cp.cc.account.been.locked=ãã¹ã¯ã¼ãã5åé£ç¶ã§ééãããããããä¸æçã«ã¢ã«ã¦ã³ããã­ãã¯ããã¾ããããææ°ã§ããã60åå¾ã«ååº¦ãè©¦ããã ããã
cp.cc.user.identity.incomplete=ã¦ã¼ã¶ã¼ã®ä¸æè¦ç´ æå ±ãä¸å®å¨ã§ãã
cp.cc.primary.channel.customer.not.found=ã¡ã¤ã³ã¢ã«ã¦ã³ãã¯å­å¨ãã¾ããã
cp.cc.user.existing=ã¦ã¼ã¶ã¼ãå­å¨ãã¾ã,ã¦ã¼ã¶ã¼æå ±ãç¢ºèªãã¦ãã ããã
cp.cc.user.element.not.match=ã¦ã¼ã¶ã¼æå ±ãä¸è´ãã¾ããã
cp.cc.channel.customers.not.found=ã¢ã«ã¦ã³ãã¯å­å¨ãã¾ããã
cp.cc.user.not.found=ã¦ã¼ã¶ã¼ã¯è¦ã¤ããã¾ããã§ããã
web.authentication.failed=èªè¨¼ã«å¤±æãã¾ããã®ã§ãåã­ã°ã¤ã³ããé¡ããã¾ãã
integration.user.unique.element.missing=å¯ä¸ã®è¦ç´ éç½®ãæ¬ è½ãã¦ãã¾ãã
cp.pos.portal.application.element.config.empty=ãµã¼ãã¹ã¯ã¾ã å®äºãã¦ãã¾ãã {0} ã«ã¤ãã¦ã®æ§æã
cp.pp.pos.portal.item.configure.not.found=ä¿å¨é ç®éç½®ãè¦ã¤ããã¾ããã§ããã
cp.pp.pos.service.unavailable=ãã¿ã¾ãã.ã·ã¹ãã ã¨ã©ã¼ãçºçãã¾ããã®ã§ãç³è«ã®ãããé£çµ¡ãã ããã
cp.pp.step.filter.not.impl=Step filterã1ã¤ã§ãå®è£ããå¿è¦ãããã¾ãã
cp.service.order.pay.not.success=ç¾å¨ã®æ¯æããå®äºãã¦ãã¾ããã®ã§ãæ¯æããåéããç³è«ãã¦ãã ããã
cp.system.internal.server.error=ç³ãè¨³ãããã¾ããããæç¶ããä¸­æ­ããã¾ããã\r\nãææ°ã§ãããæåããæä½ãããç´ãã¦ãã ããã
cp.service.pos.not.allow=æå ±ã®å¤æ´ã«ã¤ãã¦ã¯ä¿éºä¼ç¤¾ã«ãåãåãããã ããã
cp.service.claim.not.allow=è«æ±ã®æåºã«ã¤ãã¦ã¯ä¿éºä¼ç¤¾ã«ãåãåãããã ããã
posStatus.DATA_ENTRY_IN_PROGRESS=ãã¼ã¿å¥åä¸­
posStatus.WAITING_FOR_APPROVAL=æ¿èªå¾ã¡
posStatus.APPROVAL_IN_PROGRESS=æ¿èªä¸­
posStatus.WAITING_FOR_COLLECTION=ééå¾ã¡
posStatus.WAITING_FOR_EFFECTIVE=æå¹åå¾ã¡
posStatus.WAITING_FOR_DATA_ENTRY=ãã¼ã¿å¥åå¾ã¡
posStatus.EFFECTIVE=å¹æç
posStatus.CANCELLED=ã­ã£ã³ã»ã«
posStatus.WITHDRAW=æ¤å
posStatus.INVALID=ç¡å¹
posStatus.REJECTED=å´ä¸
cp.service.pending.case.invalid=ãã¿ã¾ãã.ããæç¶ããä¸­æ­ããã¾ããã\r\nãæ
